{"$schema": "https://turbo.build/schema.json", "ui": "tui", "globalEnv": ["OPENAI_API_KEY", "OPENAI_BASE_URL", "OPENAI_MODEL", "OPENAI_TEMPERATURE", "OPENAI_ORGANIZATION", "OPENAI_DEFAULT_HEADERS", "OPENAI_DISABLE_PARALLEL_TOOL_CALLS", "NODE_ENV", "DATABASE_URL", "PORT"], "concurrency": "20", "tasks": {"generate": {"outputs": ["src/generated/**"]}, "build": {"dependsOn": ["^build", "generate"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "web#build": {"dependsOn": ["^build", "generate"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"dependsOn": ["^build", "generate"], "cache": false, "persistent": true}, "web#dev": {"dependsOn": ["^build", "generate"], "cache": false, "persistent": true}, "test": {"dependsOn": ["^build"], "outputs": []}, "link:global": {"cache": false}, "unlink:global": {"cache": false}}}