{"arrowParens": "always", "bracketSpacing": true, "htmlWhitespaceSensitivity": "css", "insertPragma": false, "bracketSameLine": true, "jsxSingleQuote": true, "printWidth": 120, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "semi": true, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false, "endOfLine": "auto", "plugins": ["prettier-plugin-tailwindcss"], "tailwindFunctions": ["clsx", "cn"], "tailwindAttributes": ["className"]}