@import 'tailwindcss';
@source "../../../apps/**/*.{ts,tsx}";
@source "../../../components/**/*.{ts,tsx}";
@source "../**/*.{ts,tsx}";

@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

:root {
    --background: oklch(1 0 0);
    --foreground: oklch(0.145 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);
    --primary: oklch(0.205 0 0);
    --primary-foreground: oklch(0.985 0 0);
    --secondary: oklch(0.97 0 0);
    --secondary-foreground: oklch(0.205 0 0);
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);
    --accent: oklch(0.97 0 0);
    --accent-foreground: oklch(0.205 0 0);
    --destructive: oklch(0.577 0.245 27.325);
    --destructive-foreground: oklch(0.577 0.245 27.325);
    --border: oklch(0.922 0 0);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.708 0 0);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --radius: 0.625rem;
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.205 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.708 0 0);
    --color-1: oklch(66.2% 0.225 25.9);
    --color-2: oklch(60.4% 0.26 302);
    --color-3: oklch(69.6% 0.165 251);
    --color-4: oklch(80.2% 0.134 225);
    --color-5: oklch(90.7% 0.231 133);
}

.dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.145 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.145 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.985 0 0);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.396 0.141 25.723);
    --destructive-foreground: oklch(0.637 0.237 25.331);
    --border: oklch(0.269 0 0);
    --input: oklch(0.269 0 0);
    --ring: oklch(0.556 0 0);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(0.269 0 0);
    --sidebar-ring: oklch(0.439 0 0);
    --color-1: oklch(66.2% 0.225 25.9);
    --color-2: oklch(60.4% 0.26 302);
    --color-3: oklch(69.6% 0.165 251);
    --color-4: oklch(80.2% 0.134 225);
    --color-5: oklch(90.7% 0.231 133);
}

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
    --animate-shine: shine var(--duration) infinite linear;
    @keyframes shine {
        0% {
            background-position: 0% 0%;
        }
        50% {
            background-position: 100% 100%;
        }
        to {
            background-position: 0% 0%;
        }
    }
    --animate-meteor: meteor 5s linear infinite;
    @keyframes meteor {
        0% {
            transform: rotate(var(--angle)) translateX(0);
            opacity: 1;
        }
        70% {
            opacity: 1;
        }
        100% {
            transform: rotate(var(--angle)) translateX(-500px);
            opacity: 0;
        }
    }
    --animate-background-position-spin: background-position-spin 3000ms infinite alternate;
    @keyframes background-position-spin {
        0% {
            background-position: top center;
        }
        100% {
            background-position: bottom center;
        }
    }
    --animate-line-shadow: line-shadow 15s linear infinite;
    @keyframes line-shadow {
        0% {
            background-position: 0 0;
        }
        100% {
            background-position: 100% -100%;
        }
    }
    --animate-aurora: aurora 8s ease-in-out infinite alternate;
    @keyframes aurora {
        0% {
            background-position: 0% 50%;
            transform: rotate(-5deg) scale(0.9);
        }
        25% {
            background-position: 50% 100%;
            transform: rotate(5deg) scale(1.1);
        }
        50% {
            background-position: 100% 50%;
            transform: rotate(-3deg) scale(0.95);
        }
        75% {
            background-position: 50% 0%;
            transform: rotate(3deg) scale(1.05);
        }
        100% {
            background-position: 0% 50%;
            transform: rotate(-5deg) scale(0.9);
        }
    }
    --animate-shiny-text: shiny-text 8s infinite;
    @keyframes shiny-text {
        0%,
        90%,
        100% {
            background-position: calc(-100% - var(--shiny-width)) 0;
        }
        30%,
        60% {
            background-position: calc(100% + var(--shiny-width)) 0;
        }
    }
    --animate-gradient: gradient 8s linear infinite;
    @keyframes gradient {
        to {
            background-position: var(--bg-size, 300%) 0;
        }
    }
    --animate-rainbow: rainbow var(--speed, 2s) infinite linear;
    --color-color-5: var(--color-5);
    --color-color-4: var(--color-4);
    --color-color-3: var(--color-3);
    --color-color-2: var(--color-2);
    --color-color-1: var(--color-1);
    @keyframes rainbow {
        0% {
            background-position: 0%;
        }
        100% {
            background-position: 200%;
        }
    }
    --animate-shimmer-slide: shimmer-slide var(--speed) ease-in-out infinite alternate;
    --animate-spin-around: spin-around calc(var(--speed) * 2) infinite linear;
    @keyframes shimmer-slide {
        to {
            transform: translate(calc(100cqw - 100%), 0);
        }
    }
    @keyframes spin-around {
        0% {
            transform: translateZ(0) rotate(0);
        }
        15%,
        35% {
            transform: translateZ(0) rotate(90deg);
        }
        65%,
        85% {
            transform: translateZ(0) rotate(270deg);
        }
        100% {
            transform: translateZ(0) rotate(360deg);
        }
    }
    --animate-pulse: pulse var(--duration) ease-out infinite;
    @keyframes pulse {
        0%,
        100% {
            boxshadow: 0 0 0 0 var(--pulse-color);
        }
        50% {
            boxshadow: 0 0 0 8px var(--pulse-color);
        }
    }
    --animate-rippling: rippling var(--duration) ease-out;
    @keyframes rippling {
        0% {
            opacity: 1;
        }
        100% {
            transform: scale(2);
            opacity: 0;
        }
    }
    --animate-grid: grid 15s linear infinite;
    @keyframes grid {
        0% {
            transform: translateY(-50%);
        }
        100% {
            transform: translateY(0);
        }
    }
    --animate-ripple: ripple var(--duration, 2s) ease calc(var(--i, 0) * 0.2s) infinite;
    @keyframes ripple {
        0%,
        100% {
            transform: translate(-50%, -50%) scale(1);
        }
        50% {
            transform: translate(-50%, -50%) scale(0.9);
        }
    }
    --animate-orbit: orbit calc(var(--duration) * 1s) linear infinite;
    @keyframes orbit {
        0% {
            transform: rotate(calc(var(--angle) * 1deg)) translateY(calc(var(--radius) * 1px))
                rotate(calc(var(--angle) * -1deg));
        }
        100% {
            transform: rotate(calc(var(--angle) * 1deg + 360deg)) translateY(calc(var(--radius) * 1px))
                rotate(calc((var(--angle) * -1deg) - 360deg));
        }
    }
    --animate-marquee: marquee var(--duration) infinite linear;
    --animate-marquee-vertical: marquee-vertical var(--duration) linear infinite;
    @keyframes marquee {
        from {
            transform: translateX(0);
        }
        to {
            transform: translateX(calc(-100% - var(--gap)));
        }
    }
    @keyframes marquee-vertical {
        from {
            transform: translateY(0);
        }
        to {
            transform: translateY(calc(-100% - var(--gap)));
        }
    }
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }
    body {
        @apply bg-background text-foreground;
        overflow: hidden;
        height: 100vh;
    }
    
    html {
        overflow: hidden;
        height: 100vh;
    }

    /* 统一的滚动条样式 - 参考 codeagent 实现 */
    * {
        scrollbar-width: thin;
        scrollbar-color: rgba(255, 255, 255, 0.4) rgba(255, 255, 255, 0.1);
    }

    /* Webkit 滚动条样式 */
    *::-webkit-scrollbar {
        width: 12px;
        height: 12px;
    }

    *::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 6px;
        border: 1px solid rgba(255, 255, 255, 0.05);
    }

    *::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.4);
        border-radius: 6px;
        border: 2px solid transparent;
        background-clip: padding-box;
    }

    *::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.6);
    }

    *::-webkit-scrollbar-thumb:active {
        background: rgba(255, 255, 255, 0.8);
    }

    /* 暗色主题下的滚动条样式 */
    .dark *::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.05);
    }

    .dark *::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.4);
    }

    .dark *::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.6);
    }

    .dark *::-webkit-scrollbar-thumb:active {
        background: rgba(255, 255, 255, 0.8);
    }

    /* 亮色主题下的滚动条样式 */
    :root *::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.02);
    }

    :root *::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
    }

    :root *::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.3);
    }

    :root *::-webkit-scrollbar-thumb:active {
        background: rgba(0, 0, 0, 0.4);
    }
}
