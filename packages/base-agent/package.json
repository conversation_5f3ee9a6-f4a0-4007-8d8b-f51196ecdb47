{"name": "@workspace/base-agent", "version": "0.1.0", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsup", "dev": "tsup --watch", "typecheck": "tsc --noEmit", "lint": "eslint . --max-warnings 0"}, "dependencies": {"@ag-ui/client": "^0.0.28", "@ag-ui/core": "^0.0.28", "@ag-ui/encoder": "^0.0.28", "@ag-ui/proto": "^0.0.28", "@ai-sdk/openai": "^1.3.22", "ai": "^4.3.16", "openai": "4.104.0", "rxjs": "7.8.1"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "tsup": "^8.5.0", "typescript": "^5.8.3"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}}