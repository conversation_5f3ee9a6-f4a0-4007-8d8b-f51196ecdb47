import * as fs from 'fs';
import * as path from 'path';
import { DiscoveryResult, RegisteredAgent } from './types.js';
import { BaseAgent } from '@workspace/base-agent';

/**
 * 自动发现并加载 agents
 */
export async function discoverAgents(discoveryPaths: string[] = ['integrations']): Promise<DiscoveryResult> {
    const agents: RegisteredAgent[] = [];
    const errors: string[] = [];

    for (const discoveryPath of discoveryPaths) {
        try {
            console.log(`🔍 Scanning path: ${discoveryPath}`);

            // 检查路径是否存在
            if (!fs.existsSync(discoveryPath)) {
                errors.push(`Discovery path does not exist: ${discoveryPath}`);
                continue;
            }

            // 扫描目录中的 agent 包
            const entries = fs.readdirSync(discoveryPath, { withFileTypes: true });

            for (const entry of entries) {
                if (entry.isDirectory() && entry.name.endsWith('-agent')) {
                    try {
                        const agentPath = path.join(discoveryPath, entry.name);
                        const packageJsonPath = path.join(agentPath, 'package.json');

                        // 检查是否有 package.json
                        if (!fs.existsSync(packageJsonPath)) {
                            continue;
                        }

                        // 读取 package.json 获取版本信息
                        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));

                        console.log(`📦 Found agent package: ${entry.name} v${packageJson.version}`);

                        // 尝试动态导入 agent
                        const agentModule = await tryImportAgent(agentPath);

                        if (agentModule && agentModule.default) {
                            const agent = agentModule.default;

                            // 验证是否是 BaseAgent 实例
                            if (agent instanceof BaseAgent) {
                                const registeredAgent = createRegisteredAgent(agent, packageJson);
                                agents.push(registeredAgent);
                                console.log(`✅ Registered agent: ${agent.metadata.name}`);
                            } else {
                                errors.push(`Invalid agent in ${entry.name}: not a BaseAgent instance`);
                            }
                        }
                    } catch (error) {
                        const errorMsg = `Failed to load agent from ${entry.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
                        errors.push(errorMsg);
                        console.warn(`⚠️ ${errorMsg}`);
                    }
                }
            }
        } catch (error) {
            const errorMsg = `Failed to scan discovery path ${discoveryPath}: ${error instanceof Error ? error.message : 'Unknown error'}`;
            errors.push(errorMsg);
            console.error(`❌ ${errorMsg}`);
        }
    }

    return { agents, errors };
}

/**
 * 尝试动态导入 agent 模块
 */
async function tryImportAgent(agentPath: string): Promise<{ default?: BaseAgent } | null> {
    const possiblePaths = [
        path.join(agentPath, 'dist', 'index.mjs'),
        path.join(agentPath, 'dist', 'index.js'),
        path.join(agentPath, 'src', 'index.ts'),
        path.join(agentPath, 'index.js'),
        path.join(agentPath, 'index.ts')
    ];

    for (const modulePath of possiblePaths) {
        try {
            if (fs.existsSync(modulePath)) {
                return await import(modulePath);
            }
        } catch {
            // 继续尝试下一个路径
            continue;
        }
    }

    throw new Error(`No valid entry point found in ${agentPath}`);
}

/**
 * 创建注册的 agent 对象
 */
function createRegisteredAgent(agent: BaseAgent, packageJson: { version?: string }): RegisteredAgent {
    const id = generateAgentId(agent.metadata.name);

    return {
        id,
        agent,
        metadata: {
            ...agent.metadata,
            id,
            version: packageJson.version || agent.metadata.version || '0.1.0',
            createdAt: new Date(),
            updatedAt: new Date()
        },
        isActive: true
    };
}

/**
 * 生成唯一的 agent ID
 */
function generateAgentId(name: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${name.toLowerCase().replace(/\s+/g, '-')}-${timestamp}-${random}`;
}

/**
 * 手动注册 agent（用于非自动发现的场景）
 */
export function registerAgent(agent: BaseAgent, packageJson?: { version?: string }): RegisteredAgent {
    return createRegisteredAgent(agent, packageJson || {});
}
