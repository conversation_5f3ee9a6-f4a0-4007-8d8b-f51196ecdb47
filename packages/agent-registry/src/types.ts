import { BaseAgent, AgentMetadata } from '@workspace/base-agent';

export interface RegisteredAgent {
    id: string;
    agent: BaseAgent;
    metadata: AgentMetadata;
    isActive: boolean;
}

export interface AgentFilter {
    name?: string;
    capabilities?: string[];
    isActive?: boolean;
}

export interface AgentRegistryConfig {
    autoDiscovery?: boolean;
    discoveryPaths?: string[];
}

export interface DiscoveryResult {
    agents: RegisteredAgent[];
    errors: string[];
}
