{"name": "@workspace/agent-registry", "version": "0.1.0", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsup", "dev": "tsup --watch", "typecheck": "tsc --noEmit", "lint": "eslint . --max-warnings 0"}, "dependencies": {"@ag-ui/client": "^0.0.28", "@ag-ui/core": "^0.0.28", "@workspace/base-agent": "workspace:*"}, "devDependencies": {"@types/node": "^24.0.3", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "tsup": "^8.5.0", "typescript": "^5.8.3"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./server": {"types": "./dist/server.d.ts", "import": "./dist/server.js", "require": "./dist/server.cjs"}, "./discovery": {"types": "./dist/discovery.d.ts", "import": "./dist/discovery.js", "require": "./dist/discovery.cjs"}}}