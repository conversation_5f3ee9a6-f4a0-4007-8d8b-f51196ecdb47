---
description: 
globs: 
alwaysApply: false
---
--
description: UI 组件开发指南和 shadcn/ui & magicui 使用规范
globs: ["packages/ui/**/*", "**/*.tsx", "**/*.css", "**/tailwind.config.*"]
alwaysApply: true
---

# UI 组件开发指南
### 禁止packages/ui目录修改任何代码

## shadcn/ui 集成
项目使用 shadcn/ui 作为基础组件库，位于 `packages/ui/` 目录，同时安装了magicui.design组件，位于 `packages/ui/src/components/magicui` 目录，

### 组件添加流程
```bash
# 在项目根目录运行
pnpm dlx shadcn@latest add button -c apps/web

# 这会将组件添加到 packages/ui/src/components/ 目录
```

### 组件使用方式
```tsx
import { Button } from "@workspace/ui/components/button"
```

## UI 包结构
```
packages/ui/
├── src/
│   ├── components/         # shadcn/ui & magicui 组件
│   ├── lib/                # 工具函数
│   └── styles/             # 样式文件
├── package.json
└── tailwind.config.ts      # Tailwind 配置
```

## 组件开发规范
### 组件文件结构
- 每个组件一个文件夹
- 使用 `index.ts` 导出组件
- 包含 TypeScript 类型定义
- 遵循 shadcn/ui 的设计模式

### 样式规范
- 使用 Tailwind CSS 进行样式设计
- 遵循设计系统的颜色和间距规范
- 支持暗色模式
- 使用 CSS 变量进行主题定制

### 组件 Props 设计
- 使用 TypeScript 接口定义 props
- 支持 `className` prop 进行样式扩展
- 使用 `forwardRef` 支持 ref 传递
- 提供合理的默认值

## Tailwind 配置
- 主配置文件: `apps/web/tailwind.config.ts`
- 全局样式: `apps/web/src/app/globals.css`
- UI 包配置: `packages/ui/tailwind.config.ts`

## 组件测试
- 为每个组件编写单元测试
- 测试组件的各种状态和 props
- 使用 Storybook 进行组件文档化（如果配置）

## 设计系统
### 颜色系统
- 使用 CSS 变量定义颜色
- 支持亮色和暗色主题
- 遵循无障碍设计标准

### 间距系统
- 使用 Tailwind 的间距标准
- 保持一致的间距比例
- 使用语义化的间距名称

### 字体系统
- 定义清晰的字体层级
- 使用合适的行高和字重
- 确保跨平台字体兼容性

## 组件库维护
### 版本管理
- 组件库版本与主项目同步
- 使用语义化版本控制
- 记录破坏性变更

### 文档维护
- 为每个组件提供使用示例
- 记录 props 和用法
- 提供设计指南和最佳实践

### 性能优化
- 使用 Tree Shaking 减少包大小
- 避免不必要的重渲染
- 优化组件的加载性能
