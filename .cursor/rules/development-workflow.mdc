---
description: 开发工作流指南和最佳实践
globs: []
alwaysApply: true
---

# 开发工作流指南

## 环境设置
### 必需工具
- **Node.js**: >= 22 (参考 [package.json](mdc:package.json) engines 字段)
- **pnpm**: 10.11.1 (项目指定版本)
- **Git**: 用于版本控制

### 初始化项目
```bash
# 克隆项目后
cd muse-studio
pnpm install
```

## 日常开发流程
### 启动开发环境
```bash
# 启动所有服务
pnpm dev

# 或启动特定应用
pnpm --filter web dev
```

### 代码质量检查
```bash
# 运行 linter
pnpm lint

# 格式化代码
pnpm format

# 类型检查
pnpm check-types
```

## Git 工作流
### 提交前检查
项目配置了 Git hooks (参考 [package.json](mdc:package.json) simple-git-hooks 配置)：
- 自动运行 lint-staged
- 自动格式化代码
- 自动修复 ESLint 错误

### 提交规范
- 使用清晰的提交信息
- 遵循约定式提交规范
- 每次提交应该是一个逻辑单元

## 依赖管理
### 添加依赖
```bash
# 添加到根项目
pnpm add <package>

# 添加到特定工作区
pnpm add <package> --filter <workspace>

# 添加开发依赖
pnpm add -D <package>
```

### 更新依赖
```bash
# 检查可更新的依赖
pnpm update:deps:check

# 更新所有依赖
pnpm update:deps

# 更新并安装
pnpm update:install
```

## 构建和部署
### 本地构建
```bash
# 构建所有包
pnpm build

# 构建特定包
pnpm --filter <workspace> build
```

### 生产环境
```bash
# 启动生产服务器
pnpm start
```

## 调试和测试
### 调试技巧
- 使用浏览器开发者工具
- 利用 React DevTools
- 使用 console.log 和断点调试

### 测试策略
- 单元测试：测试独立功能
- 集成测试：测试组件交互
- 端到端测试：测试完整用户流程

## 性能优化
### 开发时性能
- 使用 Turbo 的缓存机制
- 避免不必要的重新构建
- 合理使用 hot reload

### 生产性能
- 代码分割和懒加载
- 优化包大小
- 使用适当的缓存策略

## 故障排除
### 常见问题
1. **依赖安装失败**: 清理缓存后重新安装
2. **构建错误**: 检查 TypeScript 类型错误
3. **热重载不工作**: 重启开发服务器

### 清理命令
```bash
# 清理所有构建产物和依赖
pnpm clean

# 重新安装依赖
pnpm install
```

## 团队协作
### 代码审查
- 提交 PR 前自测功能
- 确保代码通过所有检查
- 提供清晰的 PR 描述

### 文档维护
- 更新相关文档
- 添加必要的代码注释
- 维护 README 和 CHANGELOG
