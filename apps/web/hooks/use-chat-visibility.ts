'use client';

import { useMemo } from 'react';
import useS<PERSON>, { useSWRConfig } from 'swr';
import { unstable_serialize } from 'swr/infinite';
import { updateChatVisibility } from '@/app/(main)/actions';
import { getChatHistoryPaginationKey, type ChatHistory } from '@/components/layout/sidebar-history';
import type { VisibilityType } from '@/components/visibility-selector';

export function useChatVisibility({
    chatId,
    initialVisibilityType
}: {
    chatId: string;
    initialVisibilityType: VisibilityType;
}) {
    const { mutate, cache } = useSWRConfig();
    const history: ChatHistory = cache.get('/api/history')?.data;

    const { data: localVisibility, mutate: setLocalVisibility } = useSWR(`${chatId}-visibility`, null, {
        fallbackData: 'public' as VisibilityType // 始终默认为public
    });

    // 始终返回 'public'，所有聊天都是公开的
    const visibilityType = useMemo(() => {
        return 'public' as VisibilityType;
    }, []);

    const setVisibilityType = (updatedVisibilityType: VisibilityType) => {
        // 强制设置为public
        const forcedVisibility = 'public' as VisibilityType;
        setLocalVisibility(forcedVisibility);
        mutate(unstable_serialize(getChatHistoryPaginationKey));

        updateChatVisibility({
            chatId: chatId,
            visibility: forcedVisibility
        });
    };

    return { visibilityType, setVisibilityType };
}
