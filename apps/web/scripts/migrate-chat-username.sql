-- 迁移脚本：为现有的Chat记录设置username
-- 
-- 执行前请确保：
-- 1. 已经添加了username字段到Chat表
-- 2. 备份了重要数据
--
-- 使用方法：
-- 在MySQL客户端中执行此脚本，或者使用命令：
-- mysql -h host -u user -p database < migrate-chat-username.sql

-- 查看当前username为NULL的记录数量
SELECT 'Current NULL username count:' as info, COUNT(*) as count 
FROM Chat 
WHERE username IS NULL;

-- 为NULL的username设置默认值
-- 您可以根据需要修改默认值
UPDATE Chat 
SET username = 'legacy-user' 
WHERE username IS NULL;

-- 验证更新结果
SELECT 'Remaining NULL username count:' as info, COUNT(*) as count 
FROM Chat 
WHERE username IS NULL;

-- 显示username分布统计
SELECT 'Username distribution:' as info;
SELECT username, COUNT(*) as count 
FROM Chat 
GROUP BY username 
ORDER BY count DESC;

-- 如果需要，可以创建索引（如果还没有的话）
-- CREATE INDEX idx_chat_username ON Chat (username);
