import type { UIMessage } from 'ai';
import { PreviewMessage, ThinkingMessage } from './message';
import { Greeting } from './greeting';
import { memo } from 'react';
import type { Vote } from '@/lib/db/schema';
import equal from 'fast-deep-equal';
import type { UseChatHelpers } from '@ai-sdk/react';
import { motion } from 'framer-motion';
import { useMessages } from '@/hooks/use-messages';

interface MessagesProps {
  chatId: string;
  status: UseChatHelpers['status'];
  votes: Array<Vote> | undefined;
  messages: Array<UIMessage>;
  setMessages: UseChatHelpers['setMessages'];
  reload: UseChatHelpers['reload'];
  isReadonly: boolean;
  isArtifactVisible: boolean;
  marioModeEnabled?: boolean;
  onConfirmMarioGeneration?: (testCase: string, metadata: any) => void;
}

function PureMessages({
  chatId,
  status,
  votes,
  messages,
  setMessages,
  reload,
  isReadonly,
  marioModeEnabled,
  onConfirmMarioGeneration
}: MessagesProps) {
  const {
    containerRef: messagesContainerRef,
    endRef: messagesEndRef,
    onViewportEnter,
    onViewportLeave,
    viewportConfig,
    hasSentMessage
  } = useMessages({
    chatId,
    status
  });

  return (
    <div ref={messagesContainerRef} className='relative flex min-w-0 flex-col gap-6 pt-4 pb-4' style={{ minHeight: '120vh' }}>
      {messages.length === 0 && <Greeting />}

      {messages.map((message, index) => (
        <PreviewMessage
          key={message.id}
          chatId={chatId}
          message={message}
          isLoading={status === 'streaming' && messages.length - 1 === index}
          vote={votes ? votes.find((vote) => vote.messageId === message.id) : undefined}
          setMessages={setMessages}
          reload={reload}
          isReadonly={isReadonly}
          requiresScrollPadding={hasSentMessage && index === messages.length - 1}
          marioModeEnabled={marioModeEnabled}
          onConfirmMarioGeneration={onConfirmMarioGeneration}
        />
      ))}

      {status === 'submitted' && messages.length > 0 && messages[messages.length - 1]?.role === 'user' && (
        <ThinkingMessage />
      )}

      <motion.div
        ref={messagesEndRef}
        className='min-h-[24px] min-w-[24px] shrink-0'
        onViewportLeave={onViewportLeave}
        onViewportEnter={onViewportEnter}
        viewport={viewportConfig}
      />
    </div>
  );
}

export const Messages = memo(PureMessages, (prevProps, nextProps) => {
  if (prevProps.isArtifactVisible && nextProps.isArtifactVisible) return true;

  if (prevProps.status !== nextProps.status) return false;
  if (prevProps.status && nextProps.status) return false;
  if (prevProps.messages.length !== nextProps.messages.length) return false;
  if (!equal(prevProps.messages, nextProps.messages)) return false;
  if (!equal(prevProps.votes, nextProps.votes)) return false;

  return true;
});
