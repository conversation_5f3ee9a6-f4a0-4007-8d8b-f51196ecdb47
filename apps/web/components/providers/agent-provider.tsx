'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { globalAgentRegistry, getAllAgents } from '@workspace/agent-registry';
import { BaseAgent } from '@workspace/base-agent';

interface AgentContextType {
    agents: BaseAgent[];
    activeAgent: BaseAgent | null;
    setActiveAgent: (agent: BaseAgent | null) => void;
    executingAgent: BaseAgent | null;
    isExecuting: boolean;
    isLoading: boolean;
    error: string | null;
    runAgent: (agentId: string, input: string) => Promise<any>;
    setExecutingAgent: (agent: BaseAgent | null) => void;
    setIsExecuting: (executing: boolean) => void;
}

const AgentContext = createContext<AgentContextType | undefined>(undefined);

export function useAgent() {
    const context = useContext(AgentContext);
    if (!context) {
        throw new Error('useAgent must be used within an AgentProvider');
    }
    return context;
}

interface AgentProviderProps {
    children: ReactNode;
}

export function AgentProvider({ children }: AgentProviderProps) {
    const [agents, setAgents] = useState<BaseAgent[]>([]);
    const [activeAgent, setActiveAgent] = useState<BaseAgent | null>(null);
    const [executingAgent, setExecutingAgent] = useState<BaseAgent | null>(null);
    const [isExecuting, setIsExecuting] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        async function initializeAgents() {
            try {
                setIsLoading(true);
                setError(null);

                console.log('初始化Agent系统...');

                // 等待服务端自动发现和注册agents
                // 这里只是获取已经注册的agents，实际注册在服务端完成
                const registeredAgents = getAllAgents();
                setAgents(registeredAgents);

                // 如果没有agents，可能是服务端还没有完成自动发现
                if (registeredAgents.length === 0) {
                    console.log('等待服务端Agent自动发现...');
                    // 等待一段时间后重试
                    setTimeout(() => {
                        const retryAgents = getAllAgents();
                        setAgents(retryAgents);
                        console.log(`重试后发现 ${retryAgents.length} 个agents`);
                    }, 1000);
                }

                // 设置默认的活跃agent
                if (registeredAgents.length > 0) {
                    const mcpAgentInstance = registeredAgents.find((agent) => agent.metadata.id === 'mcp-agent');
                    setActiveAgent(mcpAgentInstance || registeredAgents[0] || null);
                }

                console.log(`已初始化 ${registeredAgents.length} 个agents`);
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : 'Failed to initialize agents';
                setError(errorMessage);
                console.error('Agent初始化错误:', err);
            } finally {
                setIsLoading(false);
            }
        }

        initializeAgents();
    }, []);

    const runAgent = async (agentId: string, input: string): Promise<any> => {
        try {
            const agent = agents.find((a) => a.metadata.id === agentId);
            if (!agent) {
                throw new Error(`Agent not found: ${agentId}`);
            }

            console.log(`运行Agent: ${agentId}`, { input });

            // 设置执行状态
            setExecutingAgent(agent);
            setIsExecuting(true);

            // 构造运行输入
            const runInput = {
                messages: [
                    {
                        role: 'user' as const,
                        content: input
                    }
                ]
            };

            // 运行agent - 使用简化的调用方式
            let result;
            if (typeof agent.processMessage === 'function') {
                result = await agent.processMessage(input);
            } else {
                console.warn(`Agent ${agentId} does not have processMessage method`);
                result = { content: '该Agent暂不支持直接消息处理' };
            }

            return result;
        } catch (error) {
            console.error(`运行Agent失败: ${agentId}`, error);
            throw error;
        } finally {
            // 清除执行状态
            setExecutingAgent(null);
            setIsExecuting(false);
        }
    };

    const value: AgentContextType = {
        agents,
        activeAgent,
        setActiveAgent,
        executingAgent,
        isExecuting,
        isLoading,
        error,
        runAgent,
        setExecutingAgent,
        setIsExecuting
    };

    return <AgentContext.Provider value={value}>{children}</AgentContext.Provider>;
}
