'use client';

import { SidebarTrigger } from '@workspace/ui/components/sidebar';
import { memo } from 'react';
import { Badge } from '@workspace/ui/components/badge';
import { Button } from '@workspace/ui/components/button';
import {
  Lightbulb,
  PanelRightClose,
  PanelRightOpen,
  Expand,
  Shrink,
  PanelLeftClose,
  PanelLeftOpen
} from 'lucide-react';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@workspace/ui/components/resizable';
import { useSidebar } from '@workspace/ui/components/sidebar';
import { AuroraText } from '@workspace/ui/components/magicui/aurora-text';
import type { VisibilityType } from './visibility-selector';

/**
 * 自定义侧边栏切换按钮组件
 * 根据侧边栏状态显示不同的图标：展开时显示 PanelLeftClose，折叠时显示 PanelLeftOpen
 */
function CustomSidebarTrigger() {
  const { toggleSidebar, state } = useSidebar();

  return (
    <Button
      variant='ghost'
      size='icon'
      className='size-7'
      onClick={toggleSidebar}
      title={state === 'expanded' ? '折叠侧边栏' : '展开侧边栏'}>
      {state === 'expanded' ? <PanelLeftClose className='h-4 w-4' /> : <PanelLeftOpen className='h-4 w-4' />}
      <span className='sr-only'>Toggle Sidebar</span>
    </Button>
  );
}

function PureChatHeader({
  selectedModelId: _selectedModelId,
  isInArtifact = false
}: {
  chatId: string;
  selectedModelId: string;
  selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
  isInArtifact?: boolean;
}) {
  return (
    <header className='bg-background border-border sticky top-0 w-full z-10'>
      <div
        className={`flex h-12 w-full items-center justify-between border-b border-white/20 px-2 backdrop-blur-lg dark:border-gray-700/50 ${
          isInArtifact ? 'bg-muted dark:bg-background' : 'bg-white/70 dark:bg-gray-900/70'
        }`}>
        <CustomSidebarTrigger />
        <div className='flex items-center gap-2'>
          <Badge variant='default' className='animate-pulse gap-1'>
            <Lightbulb className='h-3 w-3' />
            <AuroraText>Agent Contributing</AuroraText>
          </Badge>
        </div>
      </div>
    </header>
  );
}

export const ChatHeader = memo(PureChatHeader, (prevProps, nextProps) => {
  return prevProps.selectedModelId === nextProps.selectedModelId;
});
