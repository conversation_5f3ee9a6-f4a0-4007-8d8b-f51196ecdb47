import type { Chat } from '@/lib/db/schema';
import { SidebarMenuAction, SidebarMenuButton, SidebarMenuItem } from '@workspace/ui/components/sidebar';
import Link from 'next/link';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger
} from '@workspace/ui/components/dropdown-menu';
import { MoreHorizontal, Share, Trash } from 'lucide-react';
import { memo } from 'react';

const PureChatItem = ({
    chat,
    isActive,
    onDelete,
    setOpenMobile
}: {
    chat: Chat;
    isActive: boolean;
    onDelete: (chatId: string) => void;
    setOpenMobile: (open: boolean) => void;
}) => {
    return (
        <SidebarMenuItem className='group/item'>
            <SidebarMenuButton
                asChild
                isActive={isActive}
                className='hover:bg-sidebar-accent/60 data-[active=true]:bg-sidebar-accent/80 transition-all duration-200 hover:translate-x-1 data-[active=true]:shadow-sm'>
                <Link href={`/chat/${chat.id}`} onClick={() => setOpenMobile(false)}>
                    <span className='truncate'>{chat.title}</span>
                </Link>
            </SidebarMenuButton>

            <DropdownMenu modal={true}>
                <DropdownMenuTrigger asChild>
                    <SidebarMenuAction
                        className='data-[state=open]:bg-sidebar-accent/80 data-[state=open]:text-sidebar-accent-foreground hover:bg-sidebar-accent/60 mr-0.5 transition-all duration-200 hover:scale-110'
                        showOnHover={!isActive}>
                        <MoreHorizontal />
                        <span className='sr-only'>More</span>
                    </SidebarMenuAction>
                </DropdownMenuTrigger>

                <DropdownMenuContent
                    side='bottom'
                    align='end'
                    className='bg-popover/95 border-border/50 shadow-lg backdrop-blur-sm'>
                    <DropdownMenuItem
                        className='hover:bg-accent/80 cursor-pointer transition-colors duration-200'
                        onClick={() => {
                            // 所有聊天都是公开的，这里可以添加分享链接的逻辑
                            console.log('分享聊天:', chat.id);
                        }}>
                        <Share />
                        <span>分享</span>
                    </DropdownMenuItem>

                    <DropdownMenuItem
                        className='text-destructive focus:bg-destructive/20 focus:text-destructive hover:bg-destructive/15 cursor-pointer transition-colors duration-200'
                        onSelect={() => onDelete(chat.id)}>
                        <Trash />
                        <span>删除</span>
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </SidebarMenuItem>
    );
};

export const ChatItem = memo(PureChatItem, (prevProps, nextProps) => {
    if (prevProps.isActive !== nextProps.isActive) return false;
    return true;
});
