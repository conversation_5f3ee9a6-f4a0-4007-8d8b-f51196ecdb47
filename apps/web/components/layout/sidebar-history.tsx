'use client';

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useState } from 'react';
import useSWRInfinite from 'swr/infinite';
import { motion } from 'framer-motion';

import type { Chat } from '@/lib/db/schema';
import { fetcher } from '@/lib/utils';
import { ChatItem } from './sidebar-history-item';
import { SidebarGroup, SidebarGroupContent, SidebarMenu, useSidebar } from '@workspace/ui/components/sidebar';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle
} from '@workspace/ui/components/alert-dialog';
import { toast } from '@/components/toast';

export const getChatHistoryPaginationKey = (pageIndex: number, previousPageData: ChatHistory | null) => {
    if (previousPageData && !previousPageData.hasMore) return null;

    const startingAfter = previousPageData?.chats?.at(-1)?.id;

    return `/api/history?limit=20${startingAfter ? `&startingAfter=${startingAfter}` : ''}`;
};

export interface ChatHistory {
    chats: Array<Chat>;
    hasMore: boolean;
}

function groupChatsByDate(chats: Array<Chat>) {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const lastWeek = new Date(today);
    lastWeek.setDate(lastWeek.getDate() - 7);
    const lastMonth = new Date(today);
    lastMonth.setDate(lastMonth.getDate() - 30);

    return chats.reduce(
        (groups, chat) => {
            const chatDate = new Date(chat.createdAt);

            if (chatDate >= today) {
                groups.today.push(chat);
            } else if (chatDate >= yesterday) {
                groups.yesterday.push(chat);
            } else if (chatDate >= lastWeek) {
                groups.lastWeek.push(chat);
            } else if (chatDate >= lastMonth) {
                groups.lastMonth.push(chat);
            } else {
                groups.older.push(chat);
            }

            return groups;
        },
        {
            today: [] as Array<Chat>,
            yesterday: [] as Array<Chat>,
            lastWeek: [] as Array<Chat>,
            lastMonth: [] as Array<Chat>,
            older: [] as Array<Chat>
        }
    );
}

export function SidebarHistory() {
    const { setOpenMobile } = useSidebar();
    const { id } = useParams();

    const {
        data: paginatedChatHistories,
        setSize,
        isValidating,
        isLoading,
        mutate
    } = useSWRInfinite<ChatHistory>(getChatHistoryPaginationKey, fetcher, {
        fallbackData: []
    });

    const router = useRouter();
    const [deleteId, setDeleteId] = useState<string | null>(null);
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);

    const hasReachedEnd = paginatedChatHistories
        ? paginatedChatHistories.some((page) => page.hasMore === false)
        : false;

    const hasEmptyChatHistory = paginatedChatHistories
        ? paginatedChatHistories.every((page) => page?.chats?.length === 0)
        : false;

    const handleDelete = async () => {
        try {
            const response = await fetch(`/api/chat?id=${deleteId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                mutate((chatHistories) => {
                    if (chatHistories) {
                        return chatHistories.map((chatHistory) => ({
                            ...chatHistory,
                            chats: (chatHistory?.chats || []).filter((chat) => chat.id !== deleteId)
                        }));
                    }
                });

                toast({
                    type: 'success',
                    description: '对话删除成功'
                });
            } else {
                toast({
                    type: 'error',
                    description: '删除对话失败'
                });
            }
        } catch {
            toast({
                type: 'error',
                description: '删除对话失败'
            });
        }

        setShowDeleteDialog(false);

        if (deleteId === id) {
            router.push('/');
        }
    };

    if (isLoading) {
        return (
            <SidebarGroup>
                <div className='text-sidebar-foreground/70 bg-sidebar-accent/20 mx-2 mb-1 rounded-md px-3 py-2 text-xs font-medium'>
                    今天
                </div>
                <SidebarGroupContent>
                    <div className='flex flex-col gap-1'>
                        {[44, 32, 28, 64, 52].map((item) => (
                            <div
                                key={item}
                                className='hover:bg-sidebar-accent/30 flex h-8 items-center gap-2 rounded-lg px-3 transition-colors duration-200'>
                                <div
                                    className='bg-sidebar-accent-foreground/20 h-4 max-w-[--skeleton-width] flex-1 animate-pulse rounded-md'
                                    style={
                                        {
                                            '--skeleton-width': `${item}%`
                                        } as React.CSSProperties
                                    }
                                />
                            </div>
                        ))}
                    </div>
                </SidebarGroupContent>
            </SidebarGroup>
        );
    }

    if (hasEmptyChatHistory) {
        return (
            <SidebarGroup>
                <SidebarGroupContent>
                    <div className='text-sidebar-foreground/60 bg-sidebar-accent/10 border-sidebar-border/30 mx-2 flex w-full flex-col items-center justify-center gap-3 rounded-lg border px-4 py-8 text-sm'>
                        <div className='text-2xl'>💬</div>
                        <div className='text-center leading-relaxed'>开始聊天后，您的对话记录将显示在这里！</div>
                    </div>
                </SidebarGroupContent>
            </SidebarGroup>
        );
    }

    return (
        <>
            <SidebarGroup>
                <SidebarGroupContent>
                    <SidebarMenu>
                        {paginatedChatHistories &&
                            (() => {
                                const chatsFromHistory = paginatedChatHistories.flatMap(
                                    (paginatedChatHistory) => paginatedChatHistory?.chats || []
                                );

                                const groupedChats = groupChatsByDate(chatsFromHistory);

                                return (
                                    <div className='flex flex-col gap-6'>
                                        {groupedChats.today.length > 0 && (
                                            <div>
                                                <div className='text-sidebar-foreground/70 bg-sidebar-accent/20 mx-2 mb-1 rounded-md px-3 py-2 text-xs font-medium'>
                                                    今天
                                                </div>
                                                {groupedChats.today.map((chat) => (
                                                    <ChatItem
                                                        key={chat.id}
                                                        chat={chat}
                                                        isActive={chat.id === id}
                                                        onDelete={(chatId) => {
                                                            setDeleteId(chatId);
                                                            setShowDeleteDialog(true);
                                                        }}
                                                        setOpenMobile={setOpenMobile}
                                                    />
                                                ))}
                                            </div>
                                        )}

                                        {groupedChats.yesterday.length > 0 && (
                                            <div>
                                                <div className='text-sidebar-foreground/70 bg-sidebar-accent/20 mx-2 mb-1 rounded-md px-3 py-2 text-xs font-medium'>
                                                    昨天
                                                </div>
                                                {groupedChats.yesterday.map((chat) => (
                                                    <ChatItem
                                                        key={chat.id}
                                                        chat={chat}
                                                        isActive={chat.id === id}
                                                        onDelete={(chatId) => {
                                                            setDeleteId(chatId);
                                                            setShowDeleteDialog(true);
                                                        }}
                                                        setOpenMobile={setOpenMobile}
                                                    />
                                                ))}
                                            </div>
                                        )}

                                        {groupedChats.lastWeek.length > 0 && (
                                            <div>
                                                <div className='text-sidebar-foreground/70 bg-sidebar-accent/20 mx-2 mb-1 rounded-md px-3 py-2 text-xs font-medium'>
                                                    最近7天
                                                </div>
                                                {groupedChats.lastWeek.map((chat) => (
                                                    <ChatItem
                                                        key={chat.id}
                                                        chat={chat}
                                                        isActive={chat.id === id}
                                                        onDelete={(chatId) => {
                                                            setDeleteId(chatId);
                                                            setShowDeleteDialog(true);
                                                        }}
                                                        setOpenMobile={setOpenMobile}
                                                    />
                                                ))}
                                            </div>
                                        )}

                                        {groupedChats.lastMonth.length > 0 && (
                                            <div>
                                                <div className='text-sidebar-foreground/70 bg-sidebar-accent/20 mx-2 mb-1 rounded-md px-3 py-2 text-xs font-medium'>
                                                    最近30天
                                                </div>
                                                {groupedChats.lastMonth.map((chat) => (
                                                    <ChatItem
                                                        key={chat.id}
                                                        chat={chat}
                                                        isActive={chat.id === id}
                                                        onDelete={(chatId) => {
                                                            setDeleteId(chatId);
                                                            setShowDeleteDialog(true);
                                                        }}
                                                        setOpenMobile={setOpenMobile}
                                                    />
                                                ))}
                                            </div>
                                        )}

                                        {groupedChats.older.length > 0 && (
                                            <div>
                                                <div className='text-sidebar-foreground/70 bg-sidebar-accent/20 mx-2 mb-1 rounded-md px-3 py-2 text-xs font-medium'>
                                                    更早
                                                </div>
                                                {groupedChats.older.map((chat) => (
                                                    <ChatItem
                                                        key={chat.id}
                                                        chat={chat}
                                                        isActive={chat.id === id}
                                                        onDelete={(chatId) => {
                                                            setDeleteId(chatId);
                                                            setShowDeleteDialog(true);
                                                        }}
                                                        setOpenMobile={setOpenMobile}
                                                    />
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                );
                            })()}
                    </SidebarMenu>

                    <motion.div
                        onViewportEnter={() => {
                            if (!isValidating && !hasReachedEnd) {
                                setSize((size) => size + 1);
                            }
                        }}
                    />
                </SidebarGroupContent>
            </SidebarGroup>

            <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>确定要删除这个对话吗？</AlertDialogTitle>
                        <AlertDialogDescription>此操作无法撤销。这将永久删除您的对话记录。</AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>取消</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDelete}>删除</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
}
