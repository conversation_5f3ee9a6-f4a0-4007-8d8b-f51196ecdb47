'use client';

import * as React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import Image from 'next/image';
import { Command, BotMessageSquare, MessagesSquare, LucideIcon } from 'lucide-react';
import { AuroraText } from '@workspace/ui/components/magicui/aurora-text';
import '@/styles/command.css';
import {
    Sidebar,
    SidebarContent,
    SidebarGroup,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarMenuSub,
    SidebarMenuSubButton,
    SidebarMenuSubItem,
    SidebarFooter
} from '@workspace/ui/components/sidebar';
import { NavUser } from '@/components/layout/nav-user';
import { useUser } from '@/components/providers/user-provider';
import { UserSkeleton } from '@/components/layout/user-skeleton';

// 定义子项类型
interface NavSubItem {
    title: string;
    url: string;
    isActive?: boolean;
}

// 定义主导航项类型
interface NavMainItem {
    title: string;
    url: string;
    icon?: LucideIcon;
    customIcon?: string;
    items: NavSubItem[];
}

// This is sample data.
const data: { navMain: NavMainItem[] } = {
    navMain: [
        {
            title: 'Chat',
            url: '/chat',
            icon: BotMessageSquare,
            items: []
        },
        {
            title: 'MCP',
            url: '/mcp',
            customIcon: '/llm-icons/mcp.svg',
            items: []
        },
        {
            title: 'Messages',
            url: '#',
            icon: MessagesSquare,
            items: []
        }
    ]
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
    const pathname = usePathname();
    const isRoot = pathname === '/';
    const { userData, isLoading } = useUser();

    return (
        <Sidebar variant='floating' {...props}>
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size='lg' asChild isActive={isRoot} tooltip='Muse Studio'>
                            <Link href='/'>
                                <div className='bg-sidebar-primary text-sidebar-primary-foreground relative flex aspect-square size-8 shrink-0 items-center justify-center overflow-hidden rounded-lg'>
                                    <Command className='z-10 size-4' />
                                    <div className='animate-shimmer absolute inset-0 bg-gradient-to-r' />
                                </div>
                                <div className='grid flex-1 text-left text-sm leading-tight'>
                                    <span className='text-balance text-sm font-semibold leading-none tracking-tighter sm:text-base md:text-lg lg:text-xl'>
                                        <AuroraText>Muse</AuroraText>
                                    </span>
                                </div>
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>
            <SidebarContent>
                <SidebarGroup>
                    <SidebarMenu className='gap-2'>
                        {data.navMain.map((item) => (
                            <SidebarMenuItem key={item.title}>
                                <SidebarMenuButton asChild tooltip={item.title}>
                                    <a href={item.url} className='font-medium'>
                                        {item.customIcon ? (
                                            <Image
                                                src={item.customIcon}
                                                alt={item.title}
                                                width={16}
                                                height={16}
                                                className='size-4'
                                            />
                                        ) : item.icon ? (
                                            <item.icon className='size-4' />
                                        ) : null}
                                        <span>{item.title}</span>
                                    </a>
                                </SidebarMenuButton>
                                {item.items?.length ? (
                                    <SidebarMenuSub className='ml-0 border-l-0 px-1.5'>
                                        {item.items.map((subItem) => (
                                            <SidebarMenuSubItem key={subItem.title}>
                                                <SidebarMenuSubButton asChild isActive={subItem.isActive}>
                                                    <a href={subItem.url}>{subItem.title}</a>
                                                </SidebarMenuSubButton>
                                            </SidebarMenuSubItem>
                                        ))}
                                    </SidebarMenuSub>
                                ) : null}
                            </SidebarMenuItem>
                        ))}
                    </SidebarMenu>
                </SidebarGroup>
            </SidebarContent>
            <SidebarFooter>{isLoading ? <UserSkeleton /> : <NavUser user={userData} />}</SidebarFooter>
        </Sidebar>
    );
}
