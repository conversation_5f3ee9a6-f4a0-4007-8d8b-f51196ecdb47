'use client';

import type { UIMessage } from 'ai';
import cx from 'classnames';
import type React from 'react';
import { useRef, useEffect, useCallback, memo } from 'react';
import { toast } from 'sonner';
import { useLocalStorage } from 'usehooks-ts';
import { useWindowSize } from '@/hooks/use-window-size';

import { ArrowUp, Square } from 'lucide-react';
import { Button } from '@workspace/ui/components/button';
import { Textarea } from '@workspace/ui/components/textarea';
import { SuggestedActions } from './suggested-actions';

import type { UseChatHelpers } from '@ai-sdk/react';
import { AnimatePresence, motion } from 'framer-motion';
import { ArrowDown } from 'lucide-react';
import { useScrollToBottom } from '@/hooks/use-scroll-to-bottom';
import type { VisibilityType } from './visibility-selector';
import { ModelSelector } from './model-selector';
import { useArtifactSelector } from '@/hooks/use-artifact';

function PureMultimodalInput({
    chatId,
    input,
    setInput,
    status,
    stop,
    messages,
    setMessages,
    append,
    handleSubmit,
    className,
    selectedVisibilityType,
    selectedModelId,
    isReadonly,
    onMarioModeToggle
}: {
    chatId: string;
    input: UseChatHelpers['input'];
    setInput: UseChatHelpers['setInput'];
    status: UseChatHelpers['status'];
    stop: () => void;
    messages: Array<UIMessage>;
    setMessages: UseChatHelpers['setMessages'];
    append: UseChatHelpers['append'];
    handleSubmit: UseChatHelpers['handleSubmit'];
    className?: string;
    selectedVisibilityType: VisibilityType;
    selectedModelId: string;
    isReadonly: boolean;
    onMarioModeToggle?: (enabled: boolean) => void;
}) {
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const { width } = useWindowSize();
    const isArtifactVisible = useArtifactSelector((state) => state.isVisible);

    useEffect(() => {
        if (textareaRef.current) {
            adjustHeight();
        }
    }, []);

    const adjustHeight = () => {
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = `${textareaRef.current.scrollHeight + 2}px`;
        }
    };

    const resetHeight = () => {
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = '120px';
        }
    };

    const [localStorageInput, setLocalStorageInput] = useLocalStorage('input', '');

    useEffect(() => {
        if (textareaRef.current) {
            const domValue = textareaRef.current.value;
            // Prefer DOM value over localStorage to handle hydration
            const finalValue = domValue || localStorageInput || '';
            setInput(finalValue);
            adjustHeight();
        }
        // Only run once after hydration
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        setLocalStorageInput(input);
    }, [input, setLocalStorageInput]);

    const handleInput = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
        setInput(event.target.value);
        adjustHeight();
    };

    const submitForm = useCallback(() => {
        // 清理URL中的查询参数，确保URL格式正确
        const cleanUrl = `/chat/${chatId}`;
        if (window.location.pathname !== cleanUrl) {
            window.history.replaceState({}, '', cleanUrl);
        }

        handleSubmit();

        setLocalStorageInput('');
        resetHeight();

        if (width && width > 768) {
            textareaRef.current?.focus();
        }
    }, [handleSubmit, setLocalStorageInput, width, chatId]);

    const { isAtBottom, scrollToBottom } = useScrollToBottom();

    useEffect(() => {
        if (status === 'submitted') {
            scrollToBottom();
        }
    }, [status, scrollToBottom]);

    return (
        <div className='relative flex w-full flex-col gap-4'>
            <AnimatePresence>
                {!isAtBottom && (
                    <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 10 }}
                        transition={{ type: 'spring', stiffness: 300, damping: 20 }}
                        className='absolute bottom-28 left-1/2 z-50 -translate-x-1/2'>
                        <Button
                            data-testid='scroll-to-bottom-button'
                            variant='outline'
                            size='icon'
                            className='border-border bg-background hover:bg-accent rounded-full border shadow-md'
                            onClick={(event) => {
                                event.preventDefault();
                                scrollToBottom();
                            }}>
                            <ArrowDown size={18} strokeWidth={4} />
                        </Button>
                    </motion.div>
                )}
            </AnimatePresence>

            {messages.length === 0 && (
                <SuggestedActions
                    append={append}
                    chatId={chatId}
                    selectedVisibilityType={selectedVisibilityType}
                    onMarioModeToggle={onMarioModeToggle}
                />
            )}

            <Textarea
                data-testid='multimodal-input'
                ref={textareaRef}
                placeholder='Send a message...'
                value={input}
                onChange={handleInput}
                className={cx(
                    'border-border max-h-[calc(75dvh)] min-h-[120px] resize-none overflow-hidden rounded-2xl border bg-[#F4F4F5] px-4 pb-12 pt-4 !text-base leading-relaxed transition-all duration-200',
                    input.length > 0
                        ? 'border-2 border-black bg-white shadow-lg ring-2 ring-black/10'
                        : 'border border-gray-200 hover:border-gray-300 focus:border-black focus:bg-white focus:ring-2 focus:ring-black/20',
                    className
                )}
                rows={3}
                autoFocus
                onKeyDown={(event) => {
                    if (event.key === 'Enter' && !event.shiftKey && !event.nativeEvent.isComposing) {
                        event.preventDefault();

                        if (status !== 'ready') {
                            toast.error('Please wait for the model to finish its response!');
                        } else {
                            submitForm();
                        }
                    }
                }}
            />

            <div className='absolute bottom-0 z-50 flex w-fit flex-row justify-start p-2'>
                {!isReadonly && (
                    <ModelSelector
                        selectedModelId={selectedModelId}
                        className='bg-background/90 border-border hover:bg-accent/90 text-foreground backdrop-blur-sm'
                        isCompact={isArtifactVisible}
                    />
                )}
            </div>

            <div className='absolute bottom-0 right-0 z-50 flex w-fit flex-row justify-end p-2'>
                {status === 'submitted' ? (
                    <StopButton stop={stop} setMessages={setMessages} />
                ) : (
                    <SendButton input={input} submitForm={submitForm} />
                )}
            </div>
        </div>
    );
}

export const MultimodalInput = memo(PureMultimodalInput, (prevProps, nextProps) => {
    if (prevProps.input !== nextProps.input) return false;
    if (prevProps.status !== nextProps.status) return false;
    if (prevProps.selectedVisibilityType !== nextProps.selectedVisibilityType) return false;
    if (prevProps.selectedModelId !== nextProps.selectedModelId) return false;
    if (prevProps.isReadonly !== nextProps.isReadonly) return false;

    return true;
});

function PureStopButton({ stop, setMessages }: { stop: () => void; setMessages: UseChatHelpers['setMessages'] }) {
    return (
        <Button
            data-testid='stop-button'
            className='bg-background/90 border-border hover:bg-accent/90 text-foreground h-fit rounded-full border p-2 backdrop-blur-sm dark:border-zinc-600'
            onClick={(event) => {
                event.preventDefault();
                stop();
                setMessages((messages) => messages);
            }}>
            <Square size={18} />
        </Button>
    );
}

const StopButton = memo(PureStopButton);

function PureSendButton({ submitForm, input }: { submitForm: () => void; input: string }) {
    const isActive = input.length > 0;

    return (
        <Button
            data-testid='send-button'
            size='icon'
            className={`rounded-full border backdrop-blur-sm transition-colors duration-200 ${
                isActive
                    ? 'border-black bg-black text-white hover:bg-gray-800'
                    : 'bg-background/90 border-border hover:bg-accent/90 text-foreground dark:border-zinc-600'
            }`}
            onClick={(event) => {
                event.preventDefault();
                submitForm();
            }}
            disabled={input.length === 0}>
            <ArrowUp size={18} strokeWidth={4} />
        </Button>
    );
}

const SendButton = memo(PureSendButton, (prevProps, nextProps) => {
    if (prevProps.input !== nextProps.input) return false;
    return true;
});
