'use client';

import { Message } from 'ai';
import { Button } from '@workspace/ui/components/button';
import { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react';
import { Textarea } from '@workspace/ui/components/textarea';
import { deleteTrailingMessages } from '@/app/(main)/actions';
import { UseChatHelpers } from '@ai-sdk/react';

export type MessageEditorProps = {
    message: Message;
    setMode: Dispatch<SetStateAction<'view' | 'edit'>>;
    setMessages: UseChatHelpers['setMessages'];
    reload: UseChatHelpers['reload'];
};

export function MessageEditor({ message, setMode, setMessages, reload }: MessageEditorProps) {
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

    const [draftContent, setDraftContent] = useState<string>(message.content);
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    useEffect(() => {
        if (textareaRef.current) {
            adjustHeight();
        }
    }, []);

    const adjustHeight = () => {
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = `${textareaRef.current.scrollHeight + 2}px`;
        }
    };

    const handleInput = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
        setDraftContent(event.target.value);
        adjustHeight();
    };

    return (
        <div className='flex w-full flex-col gap-2'>
            <Textarea
                data-testid='message-editor'
                ref={textareaRef}
                className='w-full resize-none overflow-hidden rounded-xl bg-transparent !text-base outline-none'
                value={draftContent}
                onChange={handleInput}
            />

            <div className='flex flex-row justify-end gap-2'>
                <Button
                    variant='outline'
                    className='h-fit px-3 py-2'
                    onClick={() => {
                        setMode('view');
                    }}>
                    Cancel
                </Button>
                <Button
                    data-testid='message-editor-send-button'
                    variant='default'
                    className='h-fit px-3 py-2'
                    disabled={isSubmitting}
                    onClick={async () => {
                        setIsSubmitting(true);

                        await deleteTrailingMessages({
                            id: message.id
                        });

                        // @ts-expect-error todo: support UIMessage in setMessages
                        setMessages((messages) => {
                            const index = messages.findIndex((m) => m.id === message.id);

                            if (index !== -1) {
                                const updatedMessage = {
                                    ...message,
                                    content: draftContent,
                                    parts: [{ type: 'text', text: draftContent }]
                                };

                                return [...messages.slice(0, index), updatedMessage];
                            }

                            return messages;
                        });

                        setMode('view');
                        reload();
                    }}>
                    {isSubmitting ? 'Sending...' : 'Send'}
                </Button>
            </div>
        </div>
    );
}
