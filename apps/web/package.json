{"name": "web", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --port 8000", "build": "NODE_ENV=test next build", "build:test": "NODE_ENV=test next build", "build:prod": "NODE_ENV=production next build", "start": "NODE_ENV=test next start --port 8000", "start:test": "NODE_ENV=test next start --port 8000", "start:prod": "NODE_ENV=production next start --port 8000", "db:push": "drizzle-kit push --force", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@ag-ui/client": "^0.0.28", "@ag-ui/core": "^0.0.28", "@ag-ui/encoder": "^0.0.28", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@codemirror/lang-javascript": "^6.2.4", "@codemirror/lang-python": "^6.2.1", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.3", "@codemirror/view": "^6.37.2", "@dp/node-kms": "^2.0.13", "@mtfe/basic-auth": "^0.3.2", "@mtfe/sso-web": "^2.6.1", "@types/uuid": "^10.0.0", "@workspace/agent-registry": "workspace:*", "@workspace/base-agent": "workspace:*", "@workspace/code-agent": "workspace:*", "@workspace/image-agent": "workspace:*", "@workspace/mario-agent": "workspace:*", "@workspace/mcp-agent": "workspace:*", "@workspace/sheet-agent": "workspace:*", "@workspace/text-agent": "workspace:*", "@workspace/ui": "workspace:*", "ai": "^4.3.16", "axios": "^1.10.0", "classnames": "^2.5.1", "codemirror": "^6.0.2", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "fast-deep-equal": "^3.1.3", "framer-motion": "^12.19.1", "lucide-react": "^0.522.0", "mysql2": "^3.14.1", "nanoid": "^5.1.5", "next": "^15.3.4", "next-themes": "^0.4.6", "orderedmap": "^2.1.1", "papaparse": "^5.5.3", "prosemirror-example-setup": "^1.2.3", "prosemirror-inputrules": "^1.5.0", "prosemirror-markdown": "^1.13.2", "prosemirror-model": "^1.25.1", "prosemirror-schema-basic": "^1.2.4", "prosemirror-schema-list": "^1.5.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.40.0", "react": "^19.1.0", "react-data-grid": "7.0.0-beta.55", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "rxjs": "^7.8.2", "sonner": "^2.0.5", "swr": "^2.3.3", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^24.0.3", "@types/papaparse": "^5.3.16", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "drizzle-kit": "^0.31.2", "typescript": "^5.8.3"}}