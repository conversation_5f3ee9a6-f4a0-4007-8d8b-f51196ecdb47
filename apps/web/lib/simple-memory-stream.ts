// 简化的内存流实现，完全不依赖外部包
interface StreamData {
    id: string;
    chunks: string[];
    isComplete: boolean;
    createdAt: Date;
    subscribers: Set<(chunk: string) => void>;
}

class SimpleMemoryStreamContext {
    private streams = new Map<string, StreamData>();
    private cleanupInterval: NodeJS.Timeout;

    constructor() {
        // 每5分钟清理一次过期的流
        this.cleanupInterval = setInterval(
            () => {
                this.cleanup();
            },
            5 * 60 * 1000
        );
    }

    private cleanup() {
        const now = new Date();
        for (const [id, stream] of this.streams.entries()) {
            // 清理超过30分钟的流
            if (now.getTime() - stream.createdAt.getTime() > 30 * 60 * 1000) {
                this.streams.delete(id);
            }
        }
    }

    // 创建或恢复流
    async getOrCreateStream(
        streamId: string,
        createStream: () => ReadableStream,
        resumeAt?: number
    ): Promise<ReadableStream | null> {
        let streamData = this.streams.get(streamId);

        if (!streamData) {
            // 创建新流
            streamData = {
                id: streamId,
                chunks: [],
                isComplete: false,
                createdAt: new Date(),
                subscribers: new Set()
            };
            this.streams.set(streamId, streamData);

            // 开始处理原始流
            this.processOriginalStream(streamId, createStream());
        }

        // 如果流已完成且没有请求恢复点，返回null
        if (streamData.isComplete && !resumeAt) {
            return null;
        }

        // 创建恢复流
        return this.createResumeStream(streamData, resumeAt || 0);
    }

    private async processOriginalStream(streamId: string, originalStream: ReadableStream) {
        const streamData = this.streams.get(streamId);
        if (!streamData) return;

        const reader = originalStream.getReader();
        const decoder = new TextDecoder();

        try {
            while (true) {
                const { done, value } = await reader.read();

                if (done) {
                    streamData.isComplete = true;
                    // 通知所有订阅者流已完成
                    streamData.subscribers.forEach((callback) => {
                        try {
                            callback('[DONE]');
                        } catch (error) {
                            console.error('Error notifying subscriber:', error);
                        }
                    });
                    break;
                }

                // 检查value的类型并进行适当的处理
                let chunk: string;
                if (value instanceof Uint8Array) {
                    // 如果是Uint8Array，直接解码
                    chunk = decoder.decode(value, { stream: true });
                } else if (typeof value === 'string') {
                    // 如果已经是字符串，直接使用
                    chunk = value;
                } else if (value && typeof value === 'object' && 'toString' in value) {
                    // 如果是其他对象类型，尝试转换为字符串
                    chunk = value.toString();
                } else {
                    // 其他情况，跳过这个chunk
                    console.warn('Unexpected chunk type:', typeof value, value);
                    continue;
                }

                streamData.chunks.push(chunk);

                // 通知所有订阅者新的数据块
                streamData.subscribers.forEach((callback) => {
                    try {
                        callback(chunk);
                    } catch (error) {
                        console.error('Error notifying subscriber:', error);
                    }
                });
            }
        } catch (error) {
            console.error('Error processing original stream:', error);
            streamData.isComplete = true;
        } finally {
            reader.releaseLock();
        }
    }

    private createResumeStream(streamData: StreamData, resumeAt: number): ReadableStream {
        return new ReadableStream({
            start(controller) {
                // 发送已有的数据块（从resumeAt开始）
                for (let i = resumeAt; i < streamData.chunks.length; i++) {
                    controller.enqueue(new TextEncoder().encode(streamData.chunks[i]));
                }

                // 如果流已完成，关闭控制器
                if (streamData.isComplete) {
                    controller.close();
                    return;
                }

                // 订阅新的数据块
                const subscriber = (chunk: string) => {
                    if (chunk === '[DONE]') {
                        controller.close();
                        streamData.subscribers.delete(subscriber);
                    } else {
                        controller.enqueue(new TextEncoder().encode(chunk));
                    }
                };

                streamData.subscribers.add(subscriber);

                // 清理函数
                return () => {
                    streamData.subscribers.delete(subscriber);
                };
            },
            cancel() {
                // 流被取消时的清理
            }
        });
    }

    // 获取流的状态信息
    getStreamInfo(streamId: string) {
        const streamData = this.streams.get(streamId);
        if (!streamData) return null;

        return {
            id: streamData.id,
            chunkCount: streamData.chunks.length,
            isComplete: streamData.isComplete,
            subscriberCount: streamData.subscribers.size,
            createdAt: streamData.createdAt
        };
    }

    // 手动清理特定流
    removeStream(streamId: string) {
        this.streams.delete(streamId);
    }

    // 获取所有流的统计信息
    getStats() {
        return {
            totalStreams: this.streams.size,
            activeStreams: Array.from(this.streams.values()).filter((s) => !s.isComplete).length,
            completedStreams: Array.from(this.streams.values()).filter((s) => s.isComplete).length
        };
    }

    destroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        this.streams.clear();
    }
}

// 创建全局实例
let globalStreamContext: SimpleMemoryStreamContext | null = null;

export function getSimpleMemoryStreamContext() {
    if (!globalStreamContext) {
        globalStreamContext = new SimpleMemoryStreamContext();
    }
    return globalStreamContext;
}
