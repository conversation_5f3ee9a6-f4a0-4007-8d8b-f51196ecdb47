import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
    const { pathname } = request.nextUrl;

    // SSO解析需要 Date 请求头
    if (pathname === '/open/api/session/userinfo') {
        // 创建一个新的响应对象，包含相应的请求头
        const response = NextResponse.next();
        response.headers.set('Date', new Date().toUTCString());
        response.headers.set('Content-Type', 'application/json;charset=UTF-8');
        response.headers.set('Access-Control-Allow-Origin', '*');
        return response;
    }

    return NextResponse.next();
}

// 配置中间件匹配的路径，只匹配需要特殊处理的 API 路径
export const config = {
    matcher: ['/open/api/session/userinfo', '/api/:path*', '/socket.io/:path*']
};
