import { MarioTestParams, <PERSON><PERSON><PERSON><PERSON>, MarioGenerationResult } from '../types.js';

// Mario 测试执行工具
export async function executeTest(params: MarioTestParams): Promise<{
    success: boolean;
    result?: any;
    error?: string;
}> {
    try {
        const response = await fetch('https://muse-app.hotel.test.sankuai.com/mario/test', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(params)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        return {
            success: true,
            result
        };
    } catch (error) {
        console.error('Mario测试执行失败:', error);
        return {
            success: false,
            error: error instanceof Error ? error.message : '测试执行失败'
        };
    }
}

// 创建容器工具
export async function createContainer(
    username: string = 'muse',
    maxRetries: number = 3
): Promise<{
    success: boolean;
    container?: MarioContainer;
    error?: string;
}> {
    console.log(`开始创建容器，用户名：${username}，最大重试次数：${maxRetries}`);

    let retryCount = 0;
    let lastError: Error | null = null;

    while (retryCount <= maxRetries) {
        try {
            if (retryCount > 0) {
                console.log(`正在进行第${retryCount}次重试创建容器...`);
                // 添加随机延迟，避免并发请求导致的端口冲突
                const delayTime = 1000 + Math.random() * 2000;
                console.log(`添加延迟: ${Math.round(delayTime)}ms`);
                await new Promise((resolve) => setTimeout(resolve, delayTime));
            }

            // 尝试创建容器
            console.log('发送创建容器请求...');
            const response = await fetch('/webapi/docker', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username
                })
            });

            console.log(`收到响应: HTTP ${response.status} ${response.statusText}`);

            if (!response.ok) {
                let errorData: any;
                try {
                    errorData = await response.json();
                    console.error('创建容器错误数据:', JSON.stringify(errorData));
                } catch (parseError) {
                    console.error('解析错误响应失败:', parseError);
                    throw new Error(`创建容器失败: ${response.status} ${response.statusText}`);
                }

                // 处理端口分配错误
                const isPortError =
                    typeof errorData === 'object' &&
                    ((errorData.error && (errorData.error.includes('port') || errorData.error.includes('端口'))) ||
                        (errorData.details &&
                            (errorData.details.includes('port') || errorData.details.includes('端口'))));

                if (isPortError && retryCount < maxRetries) {
                    lastError = new Error(`无法分配Docker容器端口，正在重试...`);
                    console.log(lastError.message);
                    retryCount++;
                    continue;
                }

                // 其他错误
                const errorMessage = errorData.error || errorData.message || '创建容器失败';
                throw new Error(errorMessage);
            }

            // 成功创建容器
            const containerData = await response.json();
            console.log('容器创建成功，响应数据:', JSON.stringify(containerData, null, 2));

            if (containerData.success && containerData.container) {
                const { id, name, webUrl, terminalUrl, webPort } = containerData.container;

                const container: MarioContainer = {
                    id,
                    name,
                    webUrl,
                    terminalUrl,
                    webPort
                };

                return {
                    success: true,
                    container
                };
            } else {
                throw new Error(containerData.error || '创建容器失败');
            }
        } catch (error) {
            console.error('创建容器失败:', error);

            const errorMessage = error instanceof Error ? error.message : String(error);
            const isConnectionRefused =
                errorMessage.includes('connection refused') || errorMessage.includes('ECONNREFUSED');

            if (isConnectionRefused && retryCount < maxRetries) {
                console.log('检测到连接被拒绝错误，将重试...');
                lastError = error instanceof Error ? error : new Error(String(error));
                retryCount++;
                await new Promise((resolve) => setTimeout(resolve, 3000));
                continue;
            }

            if (retryCount >= maxRetries) {
                return {
                    success: false,
                    error: error instanceof Error ? error.message : '创建容器失败'
                };
            }

            lastError = error instanceof Error ? error : new Error(String(error));
            retryCount++;
        }
    }

    return {
        success: false,
        error: lastError?.message || '创建容器失败，未知原因'
    };
}

// 生成Mario测试用例工具
export async function generateTestCase(params: {
    repository: string;
    branch: string;
    ecCaseId: string;
    description?: string;
}): Promise<MarioGenerationResult> {
    // 这里可以集成AI模型来生成测试用例
    // 暂时返回模拟数据
    return {
        repository: params.repository,
        branch: params.branch,
        ecCaseId: params.ecCaseId,
        testCase: `
// Mario 自动生成的测试用例
// 仓库: ${params.repository}
// 分支: ${params.branch}
// EC用例ID: ${params.ecCaseId}

import org.testng.annotations.Test;
import org.testng.Assert;

public class MarioGeneratedTest {
    
    @Test
    public void testCase_${params.ecCaseId}() {
        // 测试用例实现
        System.out.println("执行Mario生成的测试用例");
        
        // 这里添加具体的测试逻辑
        Assert.assertTrue(true, "测试用例执行成功");
    }
}
        `.trim(),
        description: params.description || `基于EC用例${params.ecCaseId}生成的Mario测试用例`
    };
}

// LLM分析工具
export async function callLLMAnalysis(type: string, content: string): Promise<string> {
    try {
        // 这里需要实现具体的LLM分析逻辑
        // 暂时返回模拟结果
        if (type === 'approve' && content.toLowerCase().includes('是')) {
            return 'YES';
        }
        return 'NO';
    } catch (error) {
        console.error('LLM分析失败:', error);
        throw error;
    }
}

// 解析Mario输入参数
export function parseMarioInput(input: string): {
    repository?: string;
    branch?: string;
    ecCaseId?: string;
    userMis?: string;
} {
    const result: {
        repository?: string;
        branch?: string;
        ecCaseId?: string;
        userMis?: string;
    } = {};

    // 解析仓库地址
    const repoMatch = input.match(/(?:仓库|repo)[:：]\s*([^\n\r]+)/i);
    if (repoMatch && repoMatch[1]) {
        result.repository = repoMatch[1].trim();
    }

    // 解析分支
    const branchMatch = input.match(/(?:分支|branch)[:：]\s*([^\n\r]+)/i);
    if (branchMatch && branchMatch[1]) {
        result.branch = branchMatch[1].trim();
    }

    // 解析EC用例ID
    const ecCaseMatch = input.match(/(?:EC用例|EC用例Id|EC用例ID)[:：]\s*([^\n\r]+)/i);
    if (ecCaseMatch && ecCaseMatch[1]) {
        result.ecCaseId = ecCaseMatch[1].trim();
    }

    // 解析用户MIS
    const misMatch = input.match(/(?:usermis|用户mis)[:：]\s*([^\n\r]+)/i);
    if (misMatch && misMatch[1]) {
        result.userMis = misMatch[1].trim();
    }

    return result;
}

// 格式化Mario消息
export function formatMarioMessage(content: string, type?: string): string {
    if (type === 'interrupt') {
        return `🔄 ${content}`;
    }

    if (content.includes('Mario用例生成已成功完成')) {
        return `✅ ${content}`;
    }

    if (content.includes('错误') || content.includes('失败')) {
        return `❌ ${content}`;
    }

    return content;
}
