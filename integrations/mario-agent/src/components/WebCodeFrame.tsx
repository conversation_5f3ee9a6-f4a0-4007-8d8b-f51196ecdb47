'use client';

import React, { useEffect, useRef, useState } from 'react';

interface WebCodeFrameProps {
    webUrl: string;
    containerId?: string;
    containerName?: string;
    containerInfo?: {
        network?: string;
        ports?: string[];
        webPort?: string;
        remotePort?: string;
        terminalPort?: string;
        ipAddress?: string;
        image?: string;
        created?: string;
        status?: string;
        cpu?: string;
        memory?: string;
        storage?: string;
    };
}

const WebCodeFrame = ({ webUrl }: WebCodeFrameProps) => {
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const [isLoaded, setIsLoaded] = useState(false);

    // 监听iframe加载状态和重置状态的key
    const webUrlKey = useRef(webUrl);

    useEffect(() => {
        // 如果URL变化了，重置加载状态
        if (webUrlKey.current !== webUrl) {
            setIsLoaded(false);
            webUrlKey.current = webUrl;
        }

        // 监听iframe加载完成事件
        const iframe = iframeRef.current;
        if (iframe) {
            const handleLoad = () => {
                console.log('代码编辑器iframe加载完成');
                setIsLoaded(true);
            };

            iframe.addEventListener('load', handleLoad);

            return () => {
                iframe.removeEventListener('load', handleLoad);
            };
        }
    }, [webUrl]);

    // 处理全屏功能
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            // F11 键切换全屏
            if (event.key === 'F11') {
                event.preventDefault();

                const iframe = iframeRef.current;
                if (!iframe) return;

                if (!document.fullscreenElement) {
                    iframe.requestFullscreen().catch((err) => {
                        console.error('无法进入全屏模式:', err);
                    });
                } else {
                    document.exitFullscreen().catch((err) => {
                        console.error('无法退出全屏模式:', err);
                    });
                }
            }
        };

        document.addEventListener('keydown', handleKeyDown);

        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, []);

    // 监听全屏状态变化
    useEffect(() => {
        const handleFullscreenChange = () => {
            const iframe = iframeRef.current;
            if (iframe) {
                if (document.fullscreenElement === iframe) {
                    iframe.style.position = 'fixed';
                    iframe.style.top = '0';
                    iframe.style.left = '0';
                    iframe.style.width = '100vw';
                    iframe.style.height = '100vh';
                    iframe.style.zIndex = '9999';
                } else {
                    iframe.style.position = '';
                    iframe.style.top = '';
                    iframe.style.left = '';
                    iframe.style.width = '';
                    iframe.style.height = '';
                    iframe.style.zIndex = '';
                }
            }
        };

        document.addEventListener('fullscreenchange', handleFullscreenChange);

        return () => {
            document.removeEventListener('fullscreenchange', handleFullscreenChange);
        };
    }, []);

    return (
        <div className='relative flex h-full w-full flex-col'>
            <iframe
                ref={iframeRef}
                src={webUrl}
                className={`h-full w-full border-0 ${isLoaded ? 'loaded' : ''}`}
                allow='clipboard-read; clipboard-write'
                title='OpenCode Server'
            />

            {/* 加载状态指示器 */}
            {!isLoaded && (
                <div className='absolute inset-0 flex items-center justify-center bg-gray-100'>
                    <div className='text-center'>
                        <div className='mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-4 border-blue-200 border-t-blue-600'></div>
                        <p className='text-sm text-gray-600'>正在加载代码编辑器...</p>
                    </div>
                </div>
            )}
        </div>
    );
};

export default WebCodeFrame;
