'use client';

import React, { useState, useEffect } from 'react';

interface MarioAnimatedNavBarProps {
    isLoading?: boolean;
}

const MarioAnimatedNavBar: React.FC<MarioAnimatedNavBarProps> = ({ isLoading = false }) => {
    const [waveOffset, setWaveOffset] = useState(0);
    const [particlePositions, setParticlePositions] = useState<
        Array<{ x: number; y: number; speed: number; size: number }>
    >([]);
    const [orbs, setOrbs] = useState<Array<{ x: number; y: number; hue: number; scale: number }>>([]);

    // Initialize particles
    useEffect(() => {
        // 创建更多但更小的粒子，均匀分布
        const particles = Array.from({ length: 20 }, (_, i) => ({
            x: 5 + ((i * 4.5) % 90), // 更均匀的水平分布
            y: 10 + ((i * 17) % 80), // 更均匀的垂直分布
            speed: 0.1 + Math.random() * 0.3, // 降低速度使动画更微妙
            size: 1 + Math.random() * 2 // 更小的粒子
        }));
        setParticlePositions(particles);

        // 创建更多但更小的能量球，均匀分布
        const orbsData = Array.from({ length: 8 }, (_, i) => ({
            x: 5 + ((i * 11) % 90), // 更均匀的水平分布
            y: 20 + ((i * 13) % 60), // 更集中在中间区域
            hue: (i * 45) % 360, // 更丰富的颜色
            scale: 0.6 + Math.random() * 0.3 // 更小的能量球
        }));
        setOrbs(orbsData);
    }, []);

    // Continuous wave animation
    useEffect(() => {
        const interval = setInterval(
            () => {
                setWaveOffset((prev) => (prev + 2) % 360);

                // Update particle positions
                setParticlePositions((prev) =>
                    prev.map((particle) => ({
                        ...particle,
                        x: (particle.x + particle.speed) % 100,
                        y: particle.y + Math.sin((particle.x + waveOffset) * 0.1) * 0.5
                    }))
                );

                // Update orb scales with faster animation when loading
                setOrbs((prev) =>
                    prev.map((orb, i) => ({
                        ...orb,
                        scale: 0.8 + 0.4 * Math.sin(((waveOffset + i * 60) * Math.PI) / 180)
                    }))
                );
            },
            isLoading ? 30 : 50
        ); // 加载状态时动画更快

        return () => clearInterval(interval);
    }, [waveOffset, isLoading]);

    return (
        <div
            className={`relative h-12 w-full ${
                isLoading
                    ? 'bg-gradient-to-r from-indigo-600/90 via-purple-600/90 to-pink-600/90'
                    : 'bg-gradient-to-r from-indigo-500/80 via-purple-500/80 to-pink-500/80'
            } overflow-hidden rounded-lg border border-white/10 shadow-sm backdrop-blur-sm`}>
            {/* Animated wave background */}
            <div className='absolute inset-0'>
                <svg className='h-full w-full' viewBox='0 0 400 64' preserveAspectRatio='none'>
                    <defs>
                        <linearGradient id='waveGradient' x1='0%' y1='0%' x2='100%' y2='0%'>
                            <stop offset='0%' stopColor='rgba(255,255,255,0.05)' />
                            <stop offset='50%' stopColor='rgba(255,255,255,0.15)' />
                            <stop offset='100%' stopColor='rgba(255,255,255,0.05)' />
                        </linearGradient>
                    </defs>
                    <path
                        d={`M0,24 Q${100 + Math.sin((waveOffset * Math.PI) / 180) * 15},${12 + Math.sin((waveOffset * Math.PI) / 90) * 6} ${200},24 T400,24 V48 H0 Z`}
                        fill='url(#waveGradient)'
                    />
                    <path
                        d={`M0,30 Q${150 + Math.sin(((waveOffset + 90) * Math.PI) / 180) * 10},${18 + Math.sin(((waveOffset + 45) * Math.PI) / 90) * 4} ${300},30 T400,30 V48 H0 Z`}
                        fill='rgba(255,255,255,0.03)'
                    />
                </svg>
            </div>

            {/* Floating particles */}
            <div className='absolute inset-0'>
                {particlePositions.map((particle, i) => (
                    <div
                        key={i}
                        className='absolute rounded-full bg-white opacity-30'
                        style={{
                            left: `${particle.x}%`,
                            top: `${particle.y}%`,
                            width: `${Math.max(1, particle.size * 0.6)}px`,
                            height: `${Math.max(1, particle.size * 0.6)}px`,
                            transform: `scale(${0.4 + 0.3 * Math.sin(((waveOffset + i * 30) * Math.PI) / 180)})`,
                            transition: 'transform 0.1s ease-out'
                        }}
                    />
                ))}
            </div>

            {/* Glowing orbs */}
            <div className='absolute inset-0'>
                {orbs.map((orb, i) => (
                    <div
                        key={i}
                        className='absolute rounded-full'
                        style={{
                            left: `${orb.x}%`,
                            top: `${orb.y}%`,
                            width: '8px',
                            height: '8px',
                            background: `radial-gradient(circle, hsl(${orb.hue}, 60%, 50%) 0%, hsl(${orb.hue}, 60%, 30%) 70%, transparent 100%)`,
                            transform: `scale(${orb.scale * 0.8})`,
                            boxShadow: `0 0 ${4 * orb.scale}px hsl(${orb.hue}, 60%, 50%, 0.3)`,
                            transition: 'transform 0.1s ease-out'
                        }}
                    />
                ))}
            </div>

            {/* Central energy pulse - 加载时更明亮 */}
            <div className='absolute inset-0 flex items-center justify-center'>
                <div
                    className='h-6 w-6 rounded-full bg-white'
                    style={{
                        opacity: isLoading ? 0.6 : 0.4,
                        transform: `scale(${0.8 + 0.2 * Math.sin((waveOffset * Math.PI) / 60)})`,
                        boxShadow: `0 0 ${12 + 6 * Math.sin((waveOffset * Math.PI) / 60)}px rgba(255,255,255,${isLoading ? 0.5 : 0.3})`,
                        background: `radial-gradient(circle, rgba(255,255,255,0.6) 0%, rgba(255,255,255,0.2) 70%, transparent 100%)`
                    }}
                />
            </div>

            {/* Bottom progress ripple - 加载时移动更快 */}
            <div className='absolute bottom-0 left-0 h-1 w-full'>
                <div
                    className='h-full bg-gradient-to-r from-transparent via-white to-transparent opacity-20'
                    style={{
                        transform: `translateX(${-100 + ((waveOffset * (isLoading ? 2.5 : 1.5)) % 200)}%)`,
                        transition: 'transform 0.1s linear'
                    }}
                />
            </div>
        </div>
    );
};

export default MarioAnimatedNavBar;
