'use client';

import React from 'react';
import { Loader, Play, GitBranch, Hash, FileCode } from 'lucide-react';

interface MarioPreviewProps {
    content: string;
    status?: 'streaming' | 'idle';
    title?: string;
    metadata?: {
        repository?: string;
        branch?: string;
        ecCaseId?: string;
        description?: string;
        [key: string]: unknown;
    };
}

export function MarioPreview({ content, status = 'idle', title, metadata }: MarioPreviewProps) {
    return (
        <div className='flex h-full w-full flex-col overflow-hidden'>
            {/* 状态指示器 */}
            {status === 'streaming' && (
                <div className='flex items-center gap-2 border-b bg-blue-50 p-3 text-sm text-blue-700'>
                    <div className='animate-spin'>
                        <Loader size={16} />
                    </div>
                    <span>正在生成Mario测试用例...</span>
                </div>
            )}

            {/* 元数据信息 */}
            {metadata && (metadata.repository || metadata.branch || metadata.ecCaseId) && (
                <div className='border-b bg-gray-50 p-3'>
                    <div className='mb-2 flex items-center gap-2 text-sm font-medium text-gray-700'>
                        <FileCode size={16} />
                        <span>测试用例信息</span>
                    </div>
                    <div className='grid gap-2 text-xs text-gray-600'>
                        {metadata.repository && (
                            <div className='flex items-center gap-2'>
                                <GitBranch size={12} />
                                <span className='font-mono'>{metadata.repository}</span>
                            </div>
                        )}
                        {metadata.branch && (
                            <div className='flex items-center gap-2'>
                                <span className='text-gray-400'>分支:</span>
                                <span className='font-mono text-blue-600'>{metadata.branch}</span>
                            </div>
                        )}
                        {metadata.ecCaseId && (
                            <div className='flex items-center gap-2'>
                                <Hash size={12} />
                                <span className='text-gray-400'>EC用例ID:</span>
                                <span className='font-mono text-green-600'>{metadata.ecCaseId}</span>
                            </div>
                        )}
                        {metadata.description && (
                            <div className='flex items-start gap-2'>
                                <span className='text-gray-400'>描述:</span>
                                <span className='text-gray-700'>{metadata.description}</span>
                            </div>
                        )}
                    </div>
                </div>
            )}

            {/* 代码预览区域 */}
            <div className='flex-1 overflow-auto'>
                {content ? (
                    <div className='h-full'>
                        <pre className='h-full w-full overflow-auto bg-gray-900 p-4 text-sm text-gray-100'>
                            <code className='language-java'>{content}</code>
                        </pre>
                    </div>
                ) : (
                    <div className='flex h-full items-center justify-center text-gray-500'>
                        <div className='text-center'>
                            <Play size={48} className='mx-auto mb-4 text-gray-300' />
                            <p className='text-sm'>Mario测试用例将在这里显示</p>
                            <p className='mt-1 text-xs text-gray-400'>请输入仓库信息和EC用例ID来生成测试用例</p>
                        </div>
                    </div>
                )}
            </div>

            {/* 流式生成指示器 */}
            {status === 'streaming' && content && (
                <div className='absolute bottom-4 right-4'>
                    <span className='animate-pulse text-green-400'>|</span>
                </div>
            )}
        </div>
    );
}
