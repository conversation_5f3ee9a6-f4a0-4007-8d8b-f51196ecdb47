'use client';

import React, { useMemo } from 'react';
import { <PERSON><PERSON>, Clock, Bot, User, Loader2 } from 'lucide-react';
import { ExtendedMarioMessage } from '../types.js';

interface MarioMessageListProps {
    messages: ExtendedMarioMessage[];
    currentModel?: {
        name?: string;
        iconPath?: string;
    };
}

// 等待消息组件
function WaitingMessage({ isInUserMessage }: { isInUserMessage?: boolean }) {
    return (
        <div className='flex items-center'>
            <Loader2 className={`mr-2 animate-spin ${isInUserMessage ? 'text-white' : 'text-blue-500'}`} size={16} />
            <span className={`animate-pulse font-medium ${isInUserMessage ? 'text-white' : 'text-blue-500'}`}>
                等待中...
            </span>
        </div>
    );
}

// 消息气泡组件
function MarioMessageBubble({
    message,
    isUser,
    aiModelName,
    onCopy
}: {
    message: ExtendedMarioMessage;
    isUser: boolean;
    aiModelName: string;
    onCopy: (content: string) => void;
}) {
    // 检查是否是等待消息
    const isWaitingMessage = message.isWaiting === true;

    // 格式化时间
    const formatTime = (date: Date) => {
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <div className='group relative mb-4'>
            {/* 头像和名称区域 */}
            <div className={`mb-2 flex items-center ${isUser ? 'justify-end' : 'justify-start'}`}>
                {!isUser && (
                    <>
                        {/* AI助手头像 */}
                        {message.sender?.avatar ? (
                            <div className='mr-2 flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-br from-purple-500 via-pink-500 to-orange-400 text-lg shadow-lg'>
                                {message.sender.avatar}
                            </div>
                        ) : (
                            <div className='mr-2 flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-purple-600 shadow-sm'>
                                <Bot className='h-4 w-4 text-white' />
                            </div>
                        )}
                        <div className='text-sm font-semibold text-gray-800'>{message.sender?.name || aiModelName}</div>
                        {message.createdAt && (
                            <div className='ml-2 flex items-center text-xs text-gray-500'>
                                <Clock className='mr-1 h-3 w-3' />
                                <span>{formatTime(message.createdAt)}</span>
                            </div>
                        )}
                    </>
                )}

                {isUser && (
                    <>
                        {message.createdAt && (
                            <div className='mr-2 flex items-center text-xs text-gray-500'>
                                <Clock className='mr-1 h-3 w-3' />
                                <span>{formatTime(message.createdAt)}</span>
                            </div>
                        )}
                        <div className='mr-2 text-sm font-semibold text-gray-800'>用户</div>
                        <div className='flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-br from-green-500 to-blue-600 shadow-sm'>
                            <User className='h-4 w-4 text-white' />
                        </div>
                    </>
                )}
            </div>

            {/* 消息气泡 */}
            <div
                className={`rounded-2xl p-4 shadow-sm transition-all duration-200 hover:shadow-md ${
                    isUser
                        ? 'ml-auto max-w-[85%] bg-gradient-to-r from-blue-500 to-blue-600 text-white md:max-w-[70%]'
                        : 'mr-auto max-w-[85%] border border-gray-200 bg-gradient-to-r from-gray-50 to-white md:max-w-[70%]'
                }`}>
                {/* 消息内容 */}
                <div
                    className={`whitespace-pre-wrap break-words text-sm leading-relaxed ${
                        isUser ? 'text-white' : 'text-gray-800'
                    }`}>
                    {isWaitingMessage ? <WaitingMessage isInUserMessage={isUser} /> : message.content}
                </div>
            </div>

            {/* Interrupt类型消息的特殊提示 */}
            {message.isInterrupt && !isUser && message.isInterruptCompleted && (
                <div className='mr-auto mt-2 max-w-[85%] md:max-w-[70%]'>
                    <div className='flex items-center gap-2 rounded-lg border border-amber-200 bg-amber-50 px-3 py-2 text-sm text-amber-800'>
                        <div className='flex h-5 w-5 items-center justify-center rounded-full bg-amber-200'>
                            <span className='text-xs'>⏸</span>
                        </div>
                        <span className='font-medium'>Mario助手将在你回复后继续工作</span>
                    </div>
                </div>
            )}

            {/* 操作按钮 */}
            <div
                className={`mt-2 flex items-center gap-2 opacity-0 transition-opacity group-hover:opacity-100 ${
                    isUser ? 'justify-end' : 'justify-start'
                }`}>
                <button
                    onClick={() => onCopy(message.content)}
                    className='flex h-6 w-6 items-center justify-center rounded hover:bg-gray-100'
                    title='复制'>
                    <Copy className='h-3 w-3' />
                </button>
            </div>
        </div>
    );
}

export default function MarioMessageList({ messages, currentModel }: MarioMessageListProps) {
    // AI模型名称
    const aiModelName = currentModel?.name || 'Mario AI';

    // 处理复制消息
    const handleCopy = (content: string) => {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(content).catch(console.error);
        }
    };

    return (
        <div className='h-full space-y-2 bg-gradient-to-b from-blue-50/30 to-white'>
            {messages.length === 0 ? (
                <div className='flex min-h-full flex-col items-center justify-center'>
                    <div className='text-center'>
                        <div className='mb-4'>
                            <div className='mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-purple-500 via-pink-500 to-orange-400 text-3xl shadow-lg'>
                                🧙‍♂️
                            </div>
                        </div>
                        <h3 className='mb-2 text-lg font-semibold text-gray-800'>Mario助手</h3>
                        <p className='text-gray-600'>开始与 Mario助手 对话，生成您的用例</p>
                        <p className='mt-1 text-sm text-gray-500'>在下方输入您的需求或问题</p>
                    </div>
                </div>
            ) : (
                <>
                    {messages.map((message) => {
                        const isUser = message.role === 'user';

                        return (
                            <div key={message.id}>
                                <MarioMessageBubble
                                    message={message}
                                    isUser={isUser}
                                    aiModelName={aiModelName}
                                    onCopy={handleCopy}
                                />
                            </div>
                        );
                    })}
                </>
            )}
        </div>
    );
}
