'use client';

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { useMarioWebSocket } from '../hooks/useMarioWebSocket.js';
import { ExtendedMarioMessage, MarioContainer } from '../types.js';
import { MarioContainerPanel } from './MarioContainerPanel.js';
import { MarioConnectionStatus } from './MarioConnectionStatus.js';
import MarioMessageList from './MarioMessageList.js';
import MarioAnimatedNavBar from './MarioAnimatedNavBar.js';

interface MarioChatProps {
    websocketUrl: string;
    enabled: boolean;
    username?: string;
    onCreateContainer?: (container: MarioContainer) => void;
    onMuseInput?: (museInput: { port?: number; repository?: string; user_mis?: string; mario_case?: string }) => void;
    onToast?: (message: { type: 'success' | 'error'; description: string }) => void;
    onConfirmGeneration?: (testCase: string, metadata: any) => void; // 新增：确认生成时的回调
    className?: string;
}

export function MarioChat({
    websocketUrl,
    enabled,
    username = 'muse',
    onCreateContainer,
    onMuseInput,
    onToast,
    onConfirmGeneration,
    className = ''
}: MarioChatProps) {
    const [input, setInput] = useState('');

    // 容器面板状态
    const [isPanelVisible, setIsPanelVisible] = useState(false);
    const [containerId, setContainerId] = useState<string | null>(null);
    const [containerName, setContainerName] = useState<string | null>(null);
    const [webUrl, setWebUrl] = useState<string | null>(null);
    const [terminalUrl, setTerminalUrl] = useState<string | null>(null);
    const [webPort, setWebPort] = useState<number | null>(null);
    const [canvasMessage, setCanvasMessage] = useState<string>('');
    const [isContainerHealthy, setIsContainerHealthy] = useState(false);
    const [healthCheckInProgress, setHealthCheckInProgress] = useState(false);
    const [loadingMessage, setLoadingMessage] = useState('正在创建容器...');
    const [museInput, setMuseInput] = useState<{
        port?: number;
        repository?: string;
        user_mis?: string;
        mario_case?: string;
    } | null>(null);

    const healthCheckTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // 通用健康检查函数
    const performHealthCheck = useCallback(
        async (webPort: number, retryCount = 0, maxRetries = 15): Promise<boolean> => {
            console.log(`健康检查尝试 ${retryCount + 1}/${maxRetries + 1}: /api/docker/health?webPort=${webPort}`);

            try {
                const response = await fetch(`/api/docker/health?webPort=${webPort}`);
                const result = await response.json();
                console.log(`健康检查API响应 (尝试 ${retryCount + 1}):`, result);

                const isHealthy = result.success && result.status === 'healthy';

                if (isHealthy) {
                    setIsContainerHealthy(true);
                    setHealthCheckInProgress(false);
                    setLoadingMessage('容器已就绪');
                    console.log('✅ 容器健康检查成功');
                    return true;
                } else if (retryCount < maxRetries) {
                    const waitTime = Math.min(3000 + retryCount * 1000, 8000);
                    setLoadingMessage('容器启动中...');
                    console.log(`⏳ 容器尚未就绪，${waitTime / 1000}秒后重试...`);
                    return new Promise((resolve) => {
                        healthCheckTimeoutRef.current = setTimeout(async () => {
                            const success = await performHealthCheck(webPort, retryCount + 1, maxRetries);
                            resolve(success);
                        }, waitTime);
                    });
                } else {
                    setIsContainerHealthy(false);
                    setHealthCheckInProgress(false);
                    setLoadingMessage('容器启动超时');
                    console.log('❌ 容器健康检查最终失败');
                    onToast?.({
                        type: 'error',
                        description: '容器启动超时，请稍后重试或联系管理员'
                    });
                    return false;
                }
            } catch (error) {
                console.log(`健康检查API调用失败 (尝试 ${retryCount + 1}):`, error);

                if (retryCount < maxRetries) {
                    const waitTime = Math.min(3000 + retryCount * 1000, 8000);
                    setLoadingMessage(`检查容器状态... (${retryCount + 1}/${maxRetries + 1})`);
                    console.log(`⏳ 健康检查请求失败，${waitTime / 1000}秒后重试...`);
                    return new Promise((resolve) => {
                        healthCheckTimeoutRef.current = setTimeout(async () => {
                            const success = await performHealthCheck(webPort, retryCount + 1, maxRetries);
                            resolve(success);
                        }, waitTime);
                    });
                } else {
                    setIsContainerHealthy(false);
                    setHealthCheckInProgress(false);
                    setLoadingMessage('容器连接失败');
                    console.log('❌ 健康检查请求最终失败');
                    onToast?.({
                        type: 'error',
                        description: '无法连接到容器服务，请检查网络连接或稍后重试'
                    });
                    return false;
                }
            }
        },
        [onToast]
    );

    const handleCreateContainer = useCallback(
        (containerId: string, containerName: string, webUrl: string, terminalUrl: string, webPort: number) => {
            // 设置容器信息
            setContainerId(containerId);
            setContainerName(containerName);
            setWebUrl(webUrl);
            setTerminalUrl(terminalUrl);
            setWebPort(webPort);

            // 重置健康检查状态
            setIsContainerHealthy(false);
            setHealthCheckInProgress(false);

            // 显示容器面板
            setIsPanelVisible(true);

            // 如果有webPort，立即开始健康检查
            if (webPort && webPort > 0) {
                setHealthCheckInProgress(true);
                setLoadingMessage('正在检查容器健康状态...');
                performHealthCheck(webPort);
            }

            onToast?.({
                type: 'success',
                description: `容器已创建: ${containerName}`
            });

            // 调用原始的回调
            const container: MarioContainer = {
                id: containerId,
                name: containerName,
                webUrl,
                terminalUrl,
                webPort
            };
            onCreateContainer?.(container);
        },
        [onCreateContainer, performHealthCheck, onToast]
    );

    const handleMuseInput = useCallback(
        (museInput: { port?: number; repository?: string; user_mis?: string; mario_case?: string }) => {
            console.log('收到Muse输入:', museInput);
            setMuseInput(museInput);
            onMuseInput?.(museInput);
        },
        [onMuseInput]
    );

    // 监听画布tab打开事件
    useEffect(() => {
        const handleOpenCanvasTab = (event: CustomEvent) => {
            const { message } = event.detail;

            // 设置画布消息内容
            setCanvasMessage(message || '');

            // 如果面板未显示，则显示面板
            if (!isPanelVisible) {
                setIsPanelVisible(true);
            }

            onToast?.({
                type: 'success',
                description: '画布已打开，回复消息已在画布中展示'
            });
        };

        // 添加事件监听器
        window.addEventListener('openCanvasTab', handleOpenCanvasTab as EventListener);

        // 清理函数
        return () => {
            window.removeEventListener('openCanvasTab', handleOpenCanvasTab as EventListener);
        };
    }, [isPanelVisible, onToast]);

    // 执行用例测试
    const handleExecuteTest = useCallback(async () => {
        if (!museInput?.mario_case || !museInput?.port) {
            onToast?.({
                type: 'error',
                description: '执行用例测试失败：缺少必要参数：mario_case，或port'
            });
            return;
        }

        try {
            const testParams = {
                mario_case: museInput?.mario_case,
                remote_port: museInput?.port
            };

            console.log('执行测试参数:', testParams);

            const response = await fetch('https://muse-app.hotel.test.sankuai.com/mario/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testParams)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            onToast?.({
                type: 'success',
                description: '测试执行成功，用例执行完成，请查看结果'
            });

            console.log('测试结果:', result);
        } catch (error: unknown) {
            console.error('执行测试失败:', error);
            const errorMessage = error instanceof Error ? error.message : '未知错误';

            onToast?.({
                type: 'error',
                description: `测试执行失败: ${errorMessage}`
            });
        }
    }, [museInput, onToast]);

    // 发起PR
    const handleCreatePR = useCallback(async () => {
        if (!museInput?.repository || !museInput?.port || !museInput?.user_mis) {
            onToast?.({
                type: 'error',
                description: '发起PR失败：缺少必要参数：repository、port或user_mis'
            });
            return;
        }

        try {
            const prParams = {
                repo: museInput.repository,
                remote_port: museInput.port,
                user_mis: museInput.user_mis
            };

            console.log('发起PR参数:', prParams);

            const response = await fetch('https://muse-app.hotel.test.sankuai.com/mario/pr', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(prParams)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            onToast?.({
                type: 'success',
                description: 'PR发起成功，Pull Request已成功创建'
            });

            console.log('PR结果:', result);
        } catch (error: unknown) {
            console.error('发起PR失败:', error);
            const errorMessage = error instanceof Error ? error.message : '未知错误';

            onToast?.({
                type: 'error',
                description: `PR发起失败: ${errorMessage}`
            });
        }
    }, [museInput, onToast]);

    // 关闭容器面板
    const handleClosePanel = useCallback(() => {
        setIsPanelVisible(false);

        // 清理健康检查timeout
        if (healthCheckTimeoutRef.current) {
            clearTimeout(healthCheckTimeoutRef.current);
            healthCheckTimeoutRef.current = null;
        }

        // 重置容器状态
        setContainerId(null);
        setContainerName(null);
        setWebUrl(null);
        setTerminalUrl(null);
        setWebPort(null);
        setIsContainerHealthy(false);
        setHealthCheckInProgress(false);
        setCanvasMessage('');
    }, []);

    const {
        isConnected,
        isLoading,
        error,
        connectionStatus,
        messages,
        sendMessage,
        clearMessages,
        lastConnectionAttempt,
        connectionHistory
    } = useMarioWebSocket({
        url: websocketUrl,
        enabled,
        onCreateContainer: handleCreateContainer,
        onMuseInput: handleMuseInput,
        username
    });

    const handleSubmit = useCallback(
        (e: React.FormEvent) => {
            e.preventDefault();
            if (input.trim() && !isLoading) {
                sendMessage(input.trim());
                setInput('');
            }
        },
        [input, isLoading, sendMessage]
    );

    const handleInputChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setInput(e.target.value);
    }, []);

    // 处理确认生成
    const handleConfirmGeneration = useCallback(
        (message: ExtendedMarioMessage) => {
            if (onConfirmGeneration) {
                // 生成测试用例内容
                const mockTestCase = `
// Mario 自动生成的测试用例
// 仓库: ${museInput?.repository || 'ssh://*******************/nibqa/mario-demo.git'}
// 分支: master
// EC用例ID: ${museInput?.mario_case || '2182608'}

import org.testng.annotations.Test;
import org.testng.Assert;

public class MarioGeneratedTest {

    @Test
    public void testCase_${museInput?.mario_case || '2182608'}() {
        // 测试用例实现
        System.out.println("执行Mario生成的测试用例");

        // 这里添加具体的测试逻辑
        Assert.assertTrue(true, "测试用例执行成功");
    }
}
            `.trim();

                // 构建metadata
                const metadata = {
                    containerId,
                    containerName,
                    webUrl,
                    terminalUrl,
                    webPort,
                    isContainerHealthy,
                    healthCheckInProgress,
                    loadingMessage,
                    museInput
                };

                onConfirmGeneration(mockTestCase, metadata);
            }
        },
        [
            onConfirmGeneration,
            museInput,
            containerId,
            containerName,
            webUrl,
            terminalUrl,
            webPort,
            isContainerHealthy,
            healthCheckInProgress,
            loadingMessage
        ]
    );

    return (
        <div className={`flex h-full ${className}`}>
            {/* 左侧聊天面板 */}
            <div className={`flex flex-col ${isPanelVisible ? 'w-1/2' : 'w-full'} transition-all duration-300`}>
                {/* Mario动画导航条 */}
                <div className='p-3'>
                    <MarioAnimatedNavBar isLoading={isLoading} />
                </div>

                {/* 连接状态 */}
                <div className='border-b bg-gray-50 p-3'>
                    <div className='flex items-center justify-between'>
                        <MarioConnectionStatus
                            isConnected={isConnected}
                            connectionStatus={connectionStatus}
                            error={error}
                            lastConnectionAttempt={lastConnectionAttempt}
                            connectionHistory={connectionHistory}
                            className='flex-1'
                        />
                        <div className='ml-4 flex items-center gap-2'>
                            {isPanelVisible && (
                                <button
                                    onClick={handleClosePanel}
                                    className='rounded px-2 py-1 text-xs text-gray-500 hover:text-gray-700'>
                                    关闭面板
                                </button>
                            )}
                            <button
                                onClick={clearMessages}
                                className='rounded px-2 py-1 text-xs text-gray-500 hover:text-gray-700'>
                                清空对话
                            </button>
                        </div>
                    </div>
                </div>

                {/* 消息列表 */}
                <div className='flex-1 overflow-y-auto p-4'>
                    <MarioMessageList messages={messages} currentModel={{ name: 'Mario助手' }} />
                </div>

                {/* 输入区域 */}
                <form onSubmit={handleSubmit} className='border-t bg-white p-3'>
                    <div className='flex gap-2'>
                        <textarea
                            value={input}
                            onChange={handleInputChange}
                            placeholder={enabled ? '输入您的Mario用例生成需求...' : '请先启用Mario模式'}
                            disabled={!enabled || isLoading}
                            className='flex-1 resize-none rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100'
                            rows={3}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter' && !e.shiftKey) {
                                    e.preventDefault();
                                    handleSubmit(e);
                                }
                            }}
                        />
                        <button
                            type='submit'
                            disabled={!enabled || !input.trim() || isLoading}
                            className='rounded-md bg-blue-500 px-4 py-2 text-sm text-white hover:bg-blue-600 disabled:cursor-not-allowed disabled:bg-gray-300'>
                            {isLoading ? '发送中...' : '发送'}
                        </button>
                    </div>
                </form>
            </div>

            {/* 右侧容器面板 */}
            {isPanelVisible && (
                <div className='w-1/2 border-l'>
                    <MarioContainerPanel
                        visible={isPanelVisible}
                        containerId={containerId}
                        containerName={containerName}
                        webUrl={webUrl}
                        terminalUrl={terminalUrl}
                        webPort={webPort}
                        canvasMessage={canvasMessage}
                        isContainerHealthy={isContainerHealthy}
                        healthCheckInProgress={healthCheckInProgress}
                        loadingMessage={loadingMessage}
                        museInput={museInput}
                        onExecuteTest={handleExecuteTest}
                        onCreatePR={handleCreatePR}
                        onClose={handleClosePanel}
                        onToast={onToast}
                    />
                </div>
            )}
        </div>
    );
}
