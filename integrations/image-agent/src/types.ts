import { z } from 'zod';

// Agent 配置类型
export interface ImageAgentConfig {
    model?: string;
    temperature?: number;
    maxTokens?: number;
}

// 图片生成请求类型
export interface ImageGenerationRequest {
    type: 'create' | 'update';
    title?: string;
    description?: string;
    existingImage?: string;
}

// 图片生成响应类型
export interface ImageGenerationResponse {
    imageUrl: string;
    prompt: string;
    description?: string;
}

// Zod schema for validation
export const imageGenerationSchema = z.object({
    imageUrl: z.string().describe('The generated image URL or base64 data'),
    prompt: z.string().describe('The prompt used for image generation'),
    description: z.string().optional().describe('Optional description of the generated image')
});

export type ImageGenerationSchemaType = z.infer<typeof imageGenerationSchema>;
