{"name": "@workspace/text-agent", "version": "0.1.0", "type": "module", "license": "MIT", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./server": {"types": "./dist/server.d.ts", "import": "./dist/server.js", "require": "./dist/server.cjs"}}, "sideEffects": false, "files": ["dist/**"], "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "lint": "eslint . --max-warnings 0"}, "dependencies": {"@ag-ui/client": "^0.0.28", "@ag-ui/core": "^0.0.28", "@workspace/base-agent": "workspace:*", "ai": "^4.3.16", "lucide-react": "^0.522.0", "prosemirror-example-setup": "^1.2.3", "prosemirror-inputrules": "^1.5.0", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.40.0", "rxjs": "7.8.1", "zod": "^3.25.67"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "@workspace/ui": "workspace:*"}, "devDependencies": {"@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "tsup": "^8.5.0", "typescript": "^5.8.3"}}