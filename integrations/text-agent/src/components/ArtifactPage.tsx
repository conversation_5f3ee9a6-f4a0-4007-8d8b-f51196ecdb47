'use client';

import React from 'react';
import { TextEditor } from './TextEditor.js';

export interface ArtifactPageProps {
    title: string;
    content: string;
    onSaveContent?: (content: string, debounce: boolean) => void;
    metadata?: Record<string, unknown>;
    setMetadata?: (metadata: Record<string, unknown>) => void;
    status?: 'streaming' | 'idle';
    isCurrentVersion?: boolean;
    currentVersionIndex?: number;
    suggestions?: Array<Record<string, unknown>>;
    mode?: 'edit' | 'diff';
    isInline?: boolean;
    isLoading?: boolean;
}

export function TextArtifactPage({
    content,
    onSaveContent,
    status = 'idle',
    isCurrentVersion = true,
    currentVersionIndex = 0,
    suggestions = [],
    ...props
}: ArtifactPageProps) {
    return (
        <div className='h-full w-full overflow-auto'>
            <TextEditor
                content={content}
                onChange={(newContent: string) => onSaveContent?.(newContent, true)}
                status={status}
                isCurrentVersion={isCurrentVersion}
                currentVersionIndex={currentVersionIndex}
                suggestions={suggestions}
                {...props}
            />
        </div>
    );
}
