'use client';

import React from 'react';
import { Play, FileText } from 'lucide-react';
import { Button } from '@workspace/ui/components/button';
import { CodeEditor } from './CodeEditor.js';
import { Console } from './Console.js';
import { generateUUID } from '../utils.js';

interface MetadataType {
  outputs?: Array<{
    id: string;
    contents: Array<{ type: 'text' | 'image'; value: string }>;
    status: 'loading_packages' | 'in_progress' | 'completed' | 'failed';
  }>;
  [key: string]: unknown;
}

export interface ArtifactPageProps {
  title: string;
  content: string;
  onSaveContent?: (content: string, debounce: boolean) => void;
  metadata?: MetadataType;
  setMetadata?: (updater: (prev: MetadataType) => MetadataType) => void;
  status?: 'streaming' | 'idle';
  isCurrentVersion?: boolean;
  currentVersionIndex?: number;
  suggestions?: Array<Record<string, unknown>>;
  mode?: 'edit' | 'diff';
  isInline?: boolean;
  isLoading?: boolean;
}

export function CodeArtifactPage({
  content,
  onSaveContent,
  metadata,
  setMetadata,
  status = 'idle',
  isCurrentVersion = true,
  currentVersionIndex = 0
}: ArtifactPageProps) {
  const hasConsoleOutput = metadata?.outputs && metadata.outputs.length > 0;

  const handleRunCode = async () => {
    console.log('🏃 [CODE-AGENT] Starting Python execution');
    console.log('📄 [CODE-AGENT] Code to execute:', {
      codeLength: content.length,
      preview: content.substring(0, 100) + '...'
    });

    if (!setMetadata) {
      console.error('❌ [CODE-AGENT] setMetadata is not available!');
      return;
    }

    const runId = generateUUID();
    const outputContent: Array<{ type: 'text' | 'image'; value: string }> = [];

    // 添加执行开始状态
    setMetadata((prev: MetadataType) => ({
      ...prev,
      outputs: [
        ...(prev.outputs || []),
        {
          id: runId,
          contents: [],
          status: 'loading_packages'
        }
      ]
    }));

    try {
      console.log('🐍 [CODE-AGENT] Loading Pyodide instance');
      // @ts-expect-error - loadPyodide is not defined
      const currentPyodideInstance = await globalThis.loadPyodide({
        indexURL: '/js/'
      });
      console.log('✅ [CODE-AGENT] Pyodide loaded successfully');

      // 更新状态为正在执行
      setMetadata((prev: MetadataType) => ({
        ...prev,
        outputs: (prev.outputs || []).map((output) =>
          output.id === runId ? { ...output, status: 'in_progress' } : output
        )
      }));

      currentPyodideInstance.setStdout({
        batched: (output: string) => {
          console.log('📤 [CODE-AGENT] Python output:', {
            type: output.startsWith('data:image/png;base64') ? 'image' : 'text',
            length: output.length
          });
          outputContent.push({
            type: output.startsWith('data:image/png;base64') ? 'image' : 'text',
            value: output
          });
        }
      });

      currentPyodideInstance.setStderr({
        batched: (output: string) => {
          console.log('🚨 [CODE-AGENT] Python error:', output);
          outputContent.push({
            type: 'text',
            value: `Error: ${output}`
          });
        }
      });

      console.log('🔧 [CODE-AGENT] Installing required packages');
      const requiredHandlers = detectRequiredHandlers(content);

      if (requiredHandlers.includes('matplotlib')) {
        await currentPyodideInstance.loadPackage(['matplotlib']);
        await currentPyodideInstance.runPython(`
                    import matplotlib
                    matplotlib.use('Agg')
                    import matplotlib.pyplot as plt
                    import io
                    import base64
                    
                    def show():
                        buf = io.BytesIO()
                        plt.savefig(buf, format='png', dpi=150, bbox_inches='tight')
                        buf.seek(0)
                        img_base64 = base64.b64encode(buf.read()).decode('utf-8')
                        print(f"data:image/png;base64,{img_base64}")
                        plt.close()
                    
                    plt.show = show
                `);
      }

      console.log('▶️ [CODE-AGENT] Executing Python code');
      await currentPyodideInstance.runPython(content);

      console.log('✅ [CODE-AGENT] Code execution completed successfully');
      setMetadata((prev: MetadataType) => ({
        ...prev,
        outputs: (prev.outputs || []).map((output) =>
          output.id === runId
            ? {
                ...output,
                contents: outputContent,
                status: 'completed'
              }
            : output
        )
      }));
    } catch (error: unknown) {
      console.error('❌ [CODE-AGENT] Code execution failed:', error);
      outputContent.push({
        type: 'text',
        value: `Execution Error: ${error instanceof Error ? error.message : String(error)}`
      });

      setMetadata((prev: MetadataType) => ({
        ...prev,
        outputs: (prev.outputs || []).map((output) =>
          output.id === runId
            ? {
                ...output,
                contents: outputContent,
                status: 'failed'
              }
            : output
        )
      }));
    }
  };

  const handleShowLogs = async () => {
    console.log('📋 [CODE-AGENT] Showing logs');
    if (!setMetadata) return;

    setMetadata((prev: MetadataType) => ({
      ...prev,
      outputs: [
        ...(prev.outputs || []),
        {
          id: `log-${Date.now()}`,
          contents: [
            {
              type: 'text',
              value: `Execution log - ${new Date().toLocaleTimeString()}\nCode analysis completed.`
            }
          ],
          status: 'completed'
        }
      ]
    }));
  };

  return (
    <div className='flex h-full w-full flex-col overflow-hidden'>
      {/* 工具栏 - 始终可见 */}
      <div className='border-border bg-background/95 supports-[backdrop-filter]:bg-background/60 flex-none border-b backdrop-blur'>
        <div className='flex items-center justify-between p-3'>
          <div className='flex items-center gap-2'>
            <span className='text-muted-foreground text-sm font-medium'>代码编辑器</span>
          </div>
          <div className='flex gap-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={handleRunCode}
              disabled={!content.trim()}
              className='hover:border-primary/50 relative rounded-lg border-2 bg-white/80 backdrop-blur-sm transition-all duration-200 hover:scale-105 hover:shadow-md dark:bg-zinc-800/80'>
              <Play className='h-4 w-4' />
              运行代码
            </Button>
            <Button
              variant='outline'
              size='sm'
              onClick={handleShowLogs}
              className='hover:border-primary/50 relative rounded-lg border-2 bg-white/80 backdrop-blur-sm transition-all duration-200 hover:scale-105 hover:shadow-md dark:bg-zinc-800/80'>
              <FileText className='h-4 w-4' />
              查看日志
            </Button>
          </div>
        </div>
      </div>

      {/* Code Editor Section */}
      <div className={hasConsoleOutput ? 'min-h-0 flex-1 overflow-hidden' : 'flex-1 overflow-hidden'}>
        <CodeEditor
          content={content}
          onSaveContent={onSaveContent || (() => {})}
          status={status}
          isCurrentVersion={isCurrentVersion}
          currentVersionIndex={currentVersionIndex}
          suggestions={[]}
          hasConsoleOutput={hasConsoleOutput}
        />
      </div>

      {/* Console Output Section */}
      {hasConsoleOutput && (
        <div className='border-border flex-none border-t'>
          <Console
            consoleOutputs={metadata.outputs as Array<import('./Console.js').ConsoleOutput>}
            setConsoleOutputs={(outputs) => {
              if (setMetadata) {
                setMetadata(() => ({
                  ...metadata,
                  outputs: outputs as MetadataType['outputs']
                }));
              }
            }}
          />
        </div>
      )}
    </div>
  );
}

// 辅助函数
function detectRequiredHandlers(code: string): string[] {
  const handlers: string[] = ['basic'];

  if (code.includes('matplotlib') || code.includes('plt.')) {
    handlers.push('matplotlib');
  }

  return handlers;
}
