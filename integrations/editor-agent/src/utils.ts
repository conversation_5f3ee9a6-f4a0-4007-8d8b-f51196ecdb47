import { SupportedFormat } from './types.js';

export function validateFormat(format: string): format is SupportedFormat {
    const supportedFormats: SupportedFormat[] = ['markdown', 'text', 'html', 'json'];
    return supportedFormats.includes(format as SupportedFormat);
}

export function formatContent(content: string, format: SupportedFormat): string {
    switch (format) {
        case 'json':
            try {
                return JSON.stringify(JSON.parse(content), null, 2);
            } catch {
                return content;
            }
        case 'html':
            // 基本的HTML格式化
            return content.replace(/></g, '>\n<').replace(/^\s+|\s+$/gm, '');
        case 'markdown':
            // 基本的Markdown格式化
            return content.replace(/^#+ /gm, (match) => match + ' ').replace(/\n{3,}/g, '\n\n');
        default:
            return content;
    }
}

export function getFileExtension(format: SupportedFormat): string {
    const extensions: Record<SupportedFormat, string> = {
        markdown: '.md',
        text: '.txt',
        html: '.html',
        json: '.json'
    };
    return extensions[format];
}

export function extractMetadata(content: string, format: SupportedFormat) {
    const metadata = {
        wordCount: content.split(/\s+/).length,
        characterCount: content.length,
        lineCount: content.split('\n').length,
        format
    };

    if (format === 'markdown') {
        const headings = content.match(/^#+\s+.+$/gm) || [];
        return {
            ...metadata,
            headingCount: headings.length,
            headings: headings.map((h) => h.replace(/^#+\s+/, ''))
        };
    }

    if (format === 'html') {
        const tags = content.match(/<[^>]+>/g) || [];
        return {
            ...metadata,
            tagCount: tags.length
        };
    }

    return metadata;
}

export function generateDocumentId(): string {
    return `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
