export interface Document {
    id: string;
    title: string;
    content: string;
    type: 'markdown' | 'text' | 'html' | 'json';
    metadata: {
        created: Date;
        modified: Date;
        author?: string;
        tags?: string[];
    };
}

export interface EditorSettings {
    theme: 'light' | 'dark';
    fontSize: number;
    lineHeight: number;
    tabSize: number;
    wordWrap: boolean;
    showLineNumbers: boolean;
    autoSave: boolean;
}

export type SupportedFormat = 'markdown' | 'text' | 'html' | 'json';
