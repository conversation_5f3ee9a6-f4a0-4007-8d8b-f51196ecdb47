import * as React from 'react';
import { ArtifactPageProps } from '@workspace/base-agent';

export function EditorArtifactPage(props: ArtifactPageProps): React.ReactElement {
    const isMarkdown = props.content.type === 'markdown';
    const isJson = props.content.type === 'json';
    const isHtml = props.content.type === 'html';

    // 将 content 转换为字符串
    const contentStr =
        typeof props.content.content === 'string' ? props.content.content : JSON.stringify(props.content.content);

    const getFileIcon = () => {
        switch (props.content.type) {
            case 'markdown':
                return '📝';
            case 'json':
                return '⚙️';
            case 'html':
                return '🌐';
            default:
                return '📄';
        }
    };

    const getFileColor = () => {
        switch (props.content.type) {
            case 'markdown':
                return 'from-green-500 to-emerald-600';
            case 'json':
                return 'from-blue-500 to-indigo-600';
            case 'html':
                return 'from-orange-500 to-red-600';
            default:
                return 'from-gray-500 to-slate-600';
        }
    };

    const renderContent = () => {
        if (isMarkdown) {
            // 修复的Markdown渲染，使用div容器避免p标签嵌套
            const lines = contentStr.split('\n');
            const elements: React.ReactElement[] = [];

            lines.forEach((line: string, index: number) => {
                if (line.startsWith('# ')) {
                    elements.push(
                        <h1 key={index} className='mb-4 mt-6 text-2xl font-bold text-slate-800 first:mt-0'>
                            {line.substring(2)}
                        </h1>
                    );
                } else if (line.startsWith('## ')) {
                    elements.push(
                        <h2 key={index} className='mb-3 mt-5 text-xl font-semibold text-slate-700'>
                            {line.substring(3)}
                        </h2>
                    );
                } else if (line.startsWith('### ')) {
                    elements.push(
                        <h3 key={index} className='mb-2 mt-4 text-lg font-medium text-slate-600'>
                            {line.substring(4)}
                        </h3>
                    );
                } else if (line.startsWith('- ')) {
                    elements.push(
                        <div key={index} className='mb-1 ml-4 flex items-start text-slate-600'>
                            <span className='mr-2'>•</span>
                            <span>{line.substring(2)}</span>
                        </div>
                    );
                } else if (line.trim() === '') {
                    elements.push(<div key={index} className='h-3' />);
                } else if (line.trim() !== '') {
                    elements.push(
                        <div key={index} className='mb-3 leading-relaxed text-slate-600'>
                            {line}
                        </div>
                    );
                }
            });

            return (
                <article className='prose prose-sm max-w-none rounded-lg border border-slate-200 bg-white p-6'>
                    {elements}
                </article>
            );
        } else {
            return (
                <section className='h-full'>
                    <pre
                        className={`h-full overflow-auto rounded-lg p-6 font-mono text-sm leading-relaxed ${
                            isJson
                                ? 'border border-blue-200 bg-blue-50 text-blue-900'
                                : isHtml
                                  ? 'border border-orange-200 bg-orange-50 text-orange-900'
                                  : 'border border-slate-200 bg-slate-50 text-slate-900'
                        } `}>
                        {contentStr || '// 没有内容'}
                    </pre>
                </section>
            );
        }
    };

    return (
        <section className='flex h-full flex-col overflow-hidden rounded-xl border border-slate-200 bg-gradient-to-br from-slate-50 to-gray-50 shadow-lg'>
            {/* Header */}
            <header className='flex items-center justify-between border-b border-slate-200 bg-white p-6 shadow-sm'>
                <div className='flex items-center space-x-4'>
                    <span
                        className={`flex h-12 w-12 items-center justify-center bg-gradient-to-br ${getFileColor()} rounded-xl text-xl font-bold text-white shadow-md`}>
                        {getFileIcon()}
                    </span>
                    <div>
                        <h1 className='text-xl font-bold leading-tight text-slate-800'>{props.content.title}</h1>
                        <div className='mt-1 flex items-center space-x-2 text-sm text-slate-500'>
                            <span className='rounded-full bg-slate-100 px-2 py-1 text-xs font-medium text-slate-600'>
                                {props.content.type.toUpperCase()}
                            </span>
                            <span>文档编辑器</span>
                        </div>
                    </div>
                </div>
                <div className='flex items-center space-x-3'>
                    <span className='rounded-full bg-slate-100 px-3 py-1.5 text-xs text-slate-500'>
                        {contentStr.length} 字符
                    </span>
                </div>
            </header>

            {/* Content */}
            <main className='flex-1 overflow-auto p-6'>{renderContent()}</main>
        </section>
    );
}
