import { z } from 'zod';

// Agent 配置类型
export interface CodeAgentConfig {
    model?: string;
    temperature?: number;
    maxTokens?: number;
}

export interface SheetAgentConfig {
    model?: string;
    temperature?: number;
    maxTokens?: number;
}

// 代码生成请求类型
export interface CodeGenerationRequest {
    type: 'create' | 'update';
    title?: string;
    description?: string;
    existingCode?: string;
}

// 表格生成请求类型
export interface SheetGenerationRequest {
    type: 'create' | 'update';
    title?: string;
    description?: string;
    existingContent?: string;
}

// 代码生成响应类型
export interface CodeGenerationResponse {
    code: string;
    language: string;
    explanation?: string;
}

// 表格生成响应类型
export interface SheetGenerationResponse {
    csv: string;
    explanation?: string;
}

// Zod schema for validation
export const codeGenerationSchema = z.object({
    code: z.string().describe('The generated Python code'),
    language: z.string().default('python').describe('Programming language'),
    explanation: z.string().optional().describe('Optional explanation of the code')
});

export const sheetGenerationSchema = z.object({
    csv: z.string().describe('The generated CSV data'),
    explanation: z.string().optional().describe('Optional explanation of the data')
});

export type CodeGenerationSchemaType = z.infer<typeof codeGenerationSchema>;
export type SheetGenerationSchemaType = z.infer<typeof sheetGenerationSchema>;
